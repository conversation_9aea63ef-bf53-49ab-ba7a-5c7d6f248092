<?php
/**
 * Setup Sample Data for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

echo "<h2>Setting up Sample Data</h2>";
echo "<style>
    body { font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
</style>";

try {
    // 1. Add Academic Levels
    echo "<h3>1. Setting up Academic Levels</h3>";
    
    $levels = [
        ['ND1', 'National Diploma Year 1'],
        ['ND2', 'National Diploma Year 2'], 
        ['HND1', 'Higher National Diploma Year 1'],
        ['HND2', 'Higher National Diploma Year 2']
    ];
    
    foreach ($levels as $level) {
        $existing = fetchOne("SELECT id FROM academic_levels WHERE level_code = :code", ['code' => $level[0]]);
        if (!$existing) {
            execute("INSERT INTO academic_levels (level_code, level_name, created_at) VALUES (:code, :name, NOW())", [
                'code' => $level[0],
                'name' => $level[1]
            ]);
            echo "<p class='success'>✓ Added academic level: {$level[1]}</p>";
        } else {
            echo "<p class='info'>• Academic level already exists: {$level[1]}</p>";
        }
    }
    
    // 2. Add Sample Departments
    echo "<h3>2. Setting up Departments</h3>";
    
    $departments = [
        ['Computer Science', 'CS', 'Department of Computer Science and Information Technology'],
        ['Electrical Engineering', 'EE', 'Department of Electrical and Electronics Engineering'],
        ['Mechanical Engineering', 'ME', 'Department of Mechanical Engineering'],
        ['Civil Engineering', 'CE', 'Department of Civil Engineering'],
        ['Accountancy', 'ACC', 'Department of Accountancy'],
        ['Business Administration', 'BA', 'Department of Business Administration'],
        ['Mass Communication', 'MC', 'Department of Mass Communication'],
        ['Agricultural Technology', 'AT', 'Department of Agricultural Technology']
    ];
    
    foreach ($departments as $dept) {
        $existing = fetchOne("SELECT id FROM departments WHERE code = :code", ['code' => $dept[1]]);
        if (!$existing) {
            execute("INSERT INTO departments (name, code, description, created_at) VALUES (:name, :code, :desc, NOW())", [
                'name' => $dept[0],
                'code' => $dept[1],
                'desc' => $dept[2]
            ]);
            echo "<p class='success'>✓ Added department: {$dept[0]}</p>";
        } else {
            echo "<p class='info'>• Department already exists: {$dept[0]}</p>";
        }
    }
    
    // 3. Add Sample Subjects
    echo "<h3>3. Subjects System (Removed)</h3>";
    echo "<p class='info'>• Subjects system has been removed from the LMS</p>";
    echo "<p class='info'>• AI now generates questions directly for department + academic level combinations</p>";
    echo "<p class='info'>• Skipping subjects setup...</p>";

    /*
    // SUBJECTS SYSTEM REMOVED - Code commented out
    // AI now generates questions directly for department + academic level combinations

    // Get department and level IDs
    $csDept = fetchOne("SELECT id FROM departments WHERE code = 'CS'");
    $eeDept = fetchOne("SELECT id FROM departments WHERE code = 'EE'");
    $accDept = fetchOne("SELECT id FROM departments WHERE code = 'ACC'");

    $nd1Level = fetchOne("SELECT id FROM academic_levels WHERE level_code = 'ND1'");
    $nd2Level = fetchOne("SELECT id FROM academic_levels WHERE level_code = 'ND2'");

    if ($csDept && $nd1Level) {
        $subjects = [
            // Computer Science ND1
            ['Introduction to Programming', 'CS101', $csDept['id'], $nd1Level['id'], 'Basic programming concepts and logic'],
            ['Computer Fundamentals', 'CS102', $csDept['id'], $nd1Level['id'], 'Introduction to computer systems'],
            ['Mathematics for Computing', 'CS103', $csDept['id'], $nd1Level['id'], 'Mathematical foundations for computing'],

            // Computer Science ND2
            ['Data Structures', 'CS201', $csDept['id'], $nd2Level['id'], 'Advanced data structures and algorithms'],
            ['Database Systems', 'CS202', $csDept['id'], $nd2Level['id'], 'Database design and management'],
            ['Web Development', 'CS203', $csDept['id'], $nd2Level['id'], 'Web technologies and development']
        ];

        if ($eeDept) {
            $subjects = array_merge($subjects, [
                // Electrical Engineering
                ['Circuit Analysis', 'EE101', $eeDept['id'], $nd1Level['id'], 'Basic electrical circuit analysis'],
                ['Electronics', 'EE102', $eeDept['id'], $nd1Level['id'], 'Electronic devices and circuits']
            ]);
        }

        if ($accDept) {
            $subjects = array_merge($subjects, [
                // Accountancy
                ['Financial Accounting', 'ACC101', $accDept['id'], $nd1Level['id'], 'Principles of financial accounting'],
                ['Cost Accounting', 'ACC102', $accDept['id'], $nd1Level['id'], 'Cost accounting methods and techniques']
            ]);
        }

        foreach ($subjects as $subject) {
            $existing = fetchOne("SELECT id FROM subjects WHERE code = :code", ['code' => $subject[1]]);
            if (!$existing) {
                execute("INSERT INTO subjects (name, code, department_id, academic_level_id, description, created_at) VALUES (:name, :code, :dept_id, :level_id, :desc, NOW())", [
                    'name' => $subject[0],
                    'code' => $subject[1],
                    'dept_id' => $subject[2],
                    'level_id' => $subject[3],
                    'desc' => $subject[4]
                ]);
                echo "<p class='success'>✓ Added subject: {$subject[0]}</p>";
            } else {
                echo "<p class='info'>• Subject already exists: {$subject[0]}</p>";
            }
        }
    }
    */
    
    // 4. Generate Sample Questions
    echo "<h3>4. Generating Sample Questions</h3>";
    echo "<p class='info'>• Sample questions are now generated using the AI Question Generator</p>";
    echo "<p class='info'>• Questions are created for department + academic level combinations</p>";
    echo "<p class='info'>• Use the 'Generate Questions' feature in the admin panel</p>";

    /*
    // SAMPLE QUESTIONS GENERATION REMOVED - Use AI Generator instead
    $subjects = fetchAll("SELECT s.*, d.name as department_name, al.level_name FROM subjects s
                         JOIN departments d ON s.department_id = d.id
                         JOIN academic_levels al ON s.academic_level_id = al.id
                         LIMIT 3");

    foreach ($subjects as $subject) {
        $existingQuestions = fetchOne("SELECT COUNT(*) as count FROM questions WHERE subject_id = :id", ['id' => $subject['id']])['count'];

        if ($existingQuestions == 0) {
            // Generate 5 sample questions for each subject
            for ($i = 1; $i <= 5; $i++) {
                $difficulties = ['easy', 'medium', 'hard'];
                $difficulty = $difficulties[($i - 1) % 3];
                $correctAnswers = ['A', 'B', 'C', 'D'];
                $correctAnswer = $correctAnswers[rand(0, 3)];

                execute("INSERT INTO questions (
                    subject_id, question_text, option_a, option_b, option_c, option_d,
                    correct_answer, explanation, difficulty, created_at
                ) VALUES (
                    :subject_id, :question_text, :option_a, :option_b, :option_c, :option_d,
                    :correct_answer, :explanation, :difficulty, NOW()
                )", [
                    'subject_id' => $subject['id'],
                    'question_text' => "What is the fundamental concept in {$subject['name']} related to topic $i?",
                    'option_a' => "First concept related to {$subject['name']}",
                    'option_b' => "Second concept related to {$subject['name']}",
                    'option_c' => "Third concept related to {$subject['name']}",
                    'option_d' => "Fourth concept related to {$subject['name']}",
                    'correct_answer' => $correctAnswer,
                    'explanation' => "This is the correct answer because it represents the core principle in {$subject['name']}.",
                    'difficulty' => $difficulty
                ]);
            }
            echo "<p class='success'>✓ Generated 5 questions for: {$subject['name']}</p>";
        } else {
            echo "<p class='info'>• Questions already exist for: {$subject['name']} ($existingQuestions questions)</p>";
        }
    }
    */
    
    // 5. Create Sample Admin if not exists
    echo "<h3>5. Setting up Admin Account</h3>";
    
    $adminExists = fetchOne("SELECT id FROM admins WHERE username = 'admin'");
    if (!$adminExists) {
        execute("INSERT INTO admins (username, password, email, full_name, created_at) VALUES (:username, :password, :email, :name, NOW())", [
            'username' => 'admin',
            'password' => password_hash('admin123', PASSWORD_DEFAULT),
            'email' => '<EMAIL>',
            'name' => 'System Administrator'
        ]);
        echo "<p class='success'>✓ Created admin account (username: admin, password: admin123)</p>";
    } else {
        echo "<p class='info'>• Admin account already exists</p>";
    }
    
    echo "<h3>Setup Complete!</h3>";
    echo "<p class='success'>Sample data has been successfully set up. You can now:</p>";
    echo "<ul>";
    echo "<li>View departments in the Departments page</li>";
    echo "<li>Generate AI questions using the AI Questions Generator</li>";
    echo "<li>Check generated questions in the AI Questions page</li>";
    echo "<li>Login with username: <strong>admin</strong>, password: <strong>admin123</strong></li>";
    echo "</ul>";
    
    echo "<br><a href='dashboard.php' style='background: #007bff; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none;'>Go to Dashboard</a>";
    echo "<a href='test-pages.php' style='background: #28a745; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none; margin-left: 10px;'>Test Pages</a>";
    
} catch (Exception $e) {
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>";
}
?>
