<?php
/**
 * Student Profile Management for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Check if student is logged in
startSecureSession();
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'student') {
    header('Location: login.php');
    exit();
}

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_profile') {
        $firstName = trim($_POST['first_name'] ?? '');
        $lastName = trim($_POST['last_name'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        
        // Validation
        if (empty($firstName) || empty($lastName)) {
            $error = 'First name and last name are required.';
        } elseif (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = 'Please enter a valid email address.';
        } else {
            try {
                // Check if email is already used by another student
                if (!empty($email)) {
                    $existingEmail = fetchOne("
                        SELECT id FROM students 
                        WHERE email = :email AND id != :id
                    ", ['email' => $email, 'id' => $_SESSION['user_id']]);
                    
                    if ($existingEmail) {
                        $error = 'This email address is already in use.';
                    }
                }
                
                if (empty($error)) {
                    // Update profile
                    execute("
                        UPDATE students 
                        SET first_name = :first_name, last_name = :last_name, 
                            email = :email, phone = :phone, updated_at = NOW()
                        WHERE id = :id
                    ", [
                        'first_name' => $firstName,
                        'last_name' => $lastName,
                        'email' => $email,
                        'phone' => $phone,
                        'id' => $_SESSION['user_id']
                    ]);
                    
                    $message = 'Profile updated successfully!';
                    
                    // Log the update
                    insertRecord('activity_logs', [
                        'user_id' => $_SESSION['user_id'],
                        'user_type' => 'student',
                        'action' => 'profile_updated',
                        'details' => 'Student updated their profile information',
                        'ip_address' => $_SERVER['REMOTE_ADDR']
                    ]);
                }
            } catch (Exception $e) {
                $error = 'Failed to update profile. Please try again.';
            }
        }
    } elseif ($action === 'change_password') {
        $currentPassword = $_POST['current_password'] ?? '';
        $newPassword = $_POST['new_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        // Validation
        if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
            $error = 'All password fields are required.';
        } elseif ($newPassword !== $confirmPassword) {
            $error = 'New passwords do not match.';
        } elseif (strlen($newPassword) < 6) {
            $error = 'New password must be at least 6 characters long.';
        } else {
            try {
                // Verify current password
                $student = fetchOne("SELECT password FROM students WHERE id = :id", ['id' => $_SESSION['user_id']]);
                
                if (!$student || !password_verify($currentPassword, $student['password'])) {
                    $error = 'Current password is incorrect.';
                } else {
                    // Update password
                    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                    execute("
                        UPDATE students 
                        SET password = :password, updated_at = NOW()
                        WHERE id = :id
                    ", ['password' => $hashedPassword, 'id' => $_SESSION['user_id']]);
                    
                    $message = 'Password changed successfully!';
                    
                    // Log the password change
                    insertRecord('activity_logs', [
                        'user_id' => $_SESSION['user_id'],
                        'user_type' => 'student',
                        'action' => 'password_changed',
                        'details' => 'Student changed their password',
                        'ip_address' => $_SERVER['REMOTE_ADDR']
                    ]);
                }
            } catch (Exception $e) {
                $error = 'Failed to change password. Please try again.';
            }
        }
    }
}

// Get student information
$student = fetchOne("
    SELECT s.*, d.name as department_name, al.level_name
    FROM students s
    JOIN departments d ON s.department_id = d.id
    JOIN academic_levels al ON s.academic_level_id = al.id
    WHERE s.id = :id
", ['id' => $_SESSION['user_id']]);

if (!$student) {
    session_destroy();
    header('Location: login.php');
    exit();
}

// Get student statistics
$stats = fetchOne("
    SELECT 
        COUNT(*) as total_quizzes,
        AVG(score) as average_score,
        MAX(score) as best_score
    FROM quiz_sessions 
    WHERE student_id = :student_id AND status = 'completed'
", ['student_id' => $student['id']]);

$pageTitle = 'My Profile';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .profile-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .profile-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .profile-card {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #28a745, #20c997);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: white;
            margin: 0 auto 20px;
        }
        .profile-info {
            text-align: center;
        }
        .profile-info h2 {
            margin: 0 0 10px;
            color: #333;
        }
        .profile-info p {
            color: #6c757d;
            margin: 5px 0;
        }
        .stats-mini {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 20px;
        }
        .stat-mini {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .stat-mini h4 {
            margin: 0;
            color: #28a745;
            font-size: 18px;
        }
        .stat-mini p {
            margin: 5px 0 0;
            font-size: 12px;
            color: #6c757d;
        }
        .form-section {
            margin-bottom: 30px;
        }
        .form-section h3 {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        .form-group input:focus {
            outline: none;
            border-color: #28a745;
        }
        .form-group input:disabled {
            background: #f8f9fa;
            color: #6c757d;
        }
        .btn-profile {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .password-section {
            border-top: 1px solid #e9ecef;
            padding-top: 30px;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Student Portal</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>

            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="quiz.php" class="nav-item">
                    <i class="fas fa-brain"></i>
                    <span>Take Quiz</span>
                </a>
                <a href="results.php" class="nav-item">
                    <i class="fas fa-chart-line"></i>
                    <span>My Results</span>
                </a>
                <a href="profile.php" class="nav-item active">
                    <i class="fas fa-user"></i>
                    <span>Profile</span>
                </a>
                <a href="logout.php" class="nav-item">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <h4><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h4>
                        <p><?php echo htmlspecialchars($student['student_id']); ?></p>
                        <p><?php echo htmlspecialchars($student['department_name'] . ' - ' . $student['level_name']); ?></p>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="profile-container">
                <div class="page-header">
                    <h1><i class="fas fa-user"></i> My Profile</h1>
                    <p>Manage your account information and settings</p>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <div class="profile-grid">
                    <!-- Profile Summary -->
                    <div class="profile-card">
                        <div class="profile-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="profile-info">
                            <h2><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h2>
                            <p><strong><?php echo htmlspecialchars($student['student_id']); ?></strong></p>
                            <p><?php echo htmlspecialchars($student['department_name']); ?></p>
                            <p><?php echo htmlspecialchars($student['level_name']); ?></p>
                            <p><small>Member since <?php echo date('F Y', strtotime($student['created_at'])); ?></small></p>
                        </div>

                        <div class="stats-mini">
                            <div class="stat-mini">
                                <h4><?php echo number_format($stats['total_quizzes'] ?? 0); ?></h4>
                                <p>Quizzes Taken</p>
                            </div>
                            <div class="stat-mini">
                                <h4><?php echo number_format($stats['average_score'] ?? 0, 1); ?>%</h4>
                                <p>Average Score</p>
                            </div>
                            <div class="stat-mini">
                                <h4><?php echo number_format($stats['best_score'] ?? 0, 1); ?>%</h4>
                                <p>Best Score</p>
                            </div>
                        </div>
                    </div>

                    <!-- Profile Form -->
                    <div class="profile-card">
                        <form method="POST" action="">
                            <input type="hidden" name="action" value="update_profile">
                            
                            <div class="form-section">
                                <h3><i class="fas fa-user-edit"></i> Personal Information</h3>
                                
                                <div class="form-group">
                                    <label for="student_id">Student ID</label>
                                    <input type="text" id="student_id" value="<?php echo htmlspecialchars($student['student_id']); ?>" disabled>
                                </div>

                                <div class="form-group">
                                    <label for="first_name">First Name *</label>
                                    <input type="text" id="first_name" name="first_name" value="<?php echo htmlspecialchars($student['first_name']); ?>" required>
                                </div>

                                <div class="form-group">
                                    <label for="last_name">Last Name *</label>
                                    <input type="text" id="last_name" name="last_name" value="<?php echo htmlspecialchars($student['last_name']); ?>" required>
                                </div>

                                <div class="form-group">
                                    <label for="email">Email Address</label>
                                    <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($student['email'] ?? ''); ?>">
                                </div>

                                <div class="form-group">
                                    <label for="phone">Phone Number</label>
                                    <input type="tel" id="phone" name="phone" value="<?php echo htmlspecialchars($student['phone'] ?? ''); ?>">
                                </div>

                                <div class="form-group">
                                    <label for="department">Department</label>
                                    <input type="text" id="department" value="<?php echo htmlspecialchars($student['department_name']); ?>" disabled>
                                </div>

                                <div class="form-group">
                                    <label for="level">Academic Level</label>
                                    <input type="text" id="level" value="<?php echo htmlspecialchars($student['level_name']); ?>" disabled>
                                </div>

                                <button type="submit" class="btn-profile btn-primary">
                                    <i class="fas fa-save"></i> Update Profile
                                </button>
                            </div>
                        </form>

                        <!-- Password Change Section -->
                        <div class="password-section">
                            <form method="POST" action="">
                                <input type="hidden" name="action" value="change_password">
                                
                                <h3><i class="fas fa-lock"></i> Change Password</h3>
                                
                                <div class="form-group">
                                    <label for="current_password">Current Password *</label>
                                    <input type="password" id="current_password" name="current_password" required>
                                </div>

                                <div class="form-group">
                                    <label for="new_password">New Password *</label>
                                    <input type="password" id="new_password" name="new_password" required minlength="6">
                                </div>

                                <div class="form-group">
                                    <label for="confirm_password">Confirm New Password *</label>
                                    <input type="password" id="confirm_password" name="confirm_password" required minlength="6">
                                </div>

                                <button type="submit" class="btn-profile btn-primary">
                                    <i class="fas fa-key"></i> Change Password
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            
            if (newPassword !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
