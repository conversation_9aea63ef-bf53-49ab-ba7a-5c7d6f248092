<?php
/**
 * Student Quiz Results for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Check if student is logged in
startSecureSession();
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'student') {
    header('Location: login.php');
    exit();
}

// Get student information
$student = fetchOne("
    SELECT s.*, d.name as department_name, al.level_name
    FROM students s
    JOIN departments d ON s.department_id = d.id
    JOIN academic_levels al ON s.academic_level_id = al.id
    WHERE s.id = :id
", ['id' => $_SESSION['user_id']]);

if (!$student) {
    session_destroy();
    header('Location: login.php');
    exit();
}

// Get pagination parameters
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Get quiz results
$quizResults = fetchAll("
    SELECT qs.*, d.name as department_name, al.level_name,
           CASE 
               WHEN qs.score >= 90 THEN 'Excellent'
               WHEN qs.score >= 80 THEN 'Very Good'
               WHEN qs.score >= 70 THEN 'Good'
               WHEN qs.score >= 60 THEN 'Fair'
               ELSE 'Poor'
           END as grade
    FROM quiz_sessions qs
    JOIN departments d ON qs.department_id = d.id
    JOIN academic_levels al ON qs.academic_level_id = al.id
    WHERE qs.student_id = :student_id AND qs.status = 'completed'
    ORDER BY qs.completed_at DESC
    LIMIT :limit OFFSET :offset
", [
    'student_id' => $student['id'],
    'limit' => $limit,
    'offset' => $offset
]);

// Get total count for pagination
$totalResults = fetchOne("
    SELECT COUNT(*) as total 
    FROM quiz_sessions 
    WHERE student_id = :student_id AND status = 'completed'
", ['student_id' => $student['id']])['total'];

$totalPages = ceil($totalResults / $limit);

// Get overall statistics
$stats = fetchOne("
    SELECT 
        COUNT(*) as total_quizzes,
        AVG(score) as average_score,
        MAX(score) as best_score,
        MIN(score) as lowest_score,
        SUM(correct_answers) as total_correct,
        SUM(total_questions) as total_questions
    FROM quiz_sessions 
    WHERE student_id = :student_id AND status = 'completed'
", ['student_id' => $student['id']]);

$pageTitle = 'My Quiz Results';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .results-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 24px;
            color: white;
        }
        .stat-icon.total { background: linear-gradient(135deg, #28a745, #20c997); }
        .stat-icon.average { background: linear-gradient(135deg, #007bff, #0056b3); }
        .stat-icon.best { background: linear-gradient(135deg, #ffc107, #e0a800); }
        .stat-icon.accuracy { background: linear-gradient(135deg, #17a2b8, #138496); }
        
        .results-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .table-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 20px;
        }
        .grade-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .grade-excellent { background: #d4edda; color: #155724; }
        .grade-very-good { background: #cce5ff; color: #004085; }
        .grade-good { background: #fff3cd; color: #856404; }
        .grade-fair { background: #f8d7da; color: #721c24; }
        .grade-poor { background: #f5c6cb; color: #721c24; }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 30px;
            gap: 10px;
        }
        .pagination a, .pagination span {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            text-decoration: none;
            color: #495057;
        }
        .pagination a:hover {
            background: #e9ecef;
        }
        .pagination .current {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        .empty-state i {
            font-size: 48px;
            margin-bottom: 20px;
            color: #dee2e6;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Student Portal</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>

            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="quiz.php" class="nav-item">
                    <i class="fas fa-brain"></i>
                    <span>Take Quiz</span>
                </a>
                <a href="results.php" class="nav-item active">
                    <i class="fas fa-chart-line"></i>
                    <span>My Results</span>
                </a>
                <a href="profile.php" class="nav-item">
                    <i class="fas fa-user"></i>
                    <span>Profile</span>
                </a>
                <a href="logout.php" class="nav-item">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <h4><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h4>
                        <p><?php echo htmlspecialchars($student['student_id']); ?></p>
                        <p><?php echo htmlspecialchars($student['department_name'] . ' - ' . $student['level_name']); ?></p>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="results-container">
                <div class="page-header">
                    <h1><i class="fas fa-chart-line"></i> My Quiz Results</h1>
                    <p>Track your learning progress and performance</p>
                </div>

                <?php if ($stats['total_quizzes'] > 0): ?>
                    <!-- Statistics Cards -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon total">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <h3><?php echo number_format($stats['total_quizzes']); ?></h3>
                            <p>Total Quizzes</p>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon average">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <h3><?php echo number_format($stats['average_score'], 1); ?>%</h3>
                            <p>Average Score</p>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon best">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <h3><?php echo number_format($stats['best_score'], 1); ?>%</h3>
                            <p>Best Score</p>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon accuracy">
                                <i class="fas fa-bullseye"></i>
                            </div>
                            <h3><?php echo $stats['total_questions'] > 0 ? number_format(($stats['total_correct'] / $stats['total_questions']) * 100, 1) : 0; ?>%</h3>
                            <p>Overall Accuracy</p>
                        </div>
                    </div>

                    <!-- Results Table -->
                    <div class="results-table">
                        <div class="table-header">
                            <h3><i class="fas fa-history"></i> Quiz History</h3>
                        </div>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Course</th>
                                        <th>Questions</th>
                                        <th>Score</th>
                                        <th>Grade</th>
                                        <th>Time</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($quizResults as $result): ?>
                                        <tr>
                                            <td><?php echo date('M d, Y H:i', strtotime($result['completed_at'])); ?></td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($result['department_name']); ?></strong><br>
                                                <small><?php echo htmlspecialchars($result['level_name']); ?></small>
                                            </td>
                                            <td>
                                                <?php echo $result['correct_answers']; ?>/<?php echo $result['total_questions']; ?>
                                            </td>
                                            <td>
                                                <strong><?php echo number_format($result['score'], 1); ?>%</strong>
                                            </td>
                                            <td>
                                                <span class="grade-badge grade-<?php echo strtolower(str_replace(' ', '-', $result['grade'])); ?>">
                                                    <?php echo $result['grade']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php 
                                                $minutes = floor($result['time_taken'] / 60);
                                                $seconds = $result['time_taken'] % 60;
                                                echo sprintf('%02d:%02d', $minutes, $seconds);
                                                ?>
                                            </td>
                                            <td>
                                                <a href="quiz-result.php?id=<?php echo $result['id']; ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i> View Details
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>">&laquo; Previous</a>
                            <?php endif; ?>

                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                <?php if ($i == $page): ?>
                                    <span class="current"><?php echo $i; ?></span>
                                <?php else: ?>
                                    <a href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                <?php endif; ?>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                                <a href="?page=<?php echo $page + 1; ?>">Next &raquo;</a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                <?php else: ?>
                    <!-- Empty State -->
                    <div class="results-table">
                        <div class="empty-state">
                            <i class="fas fa-chart-line"></i>
                            <h3>No Quiz Results Yet</h3>
                            <p>You haven't completed any quizzes yet. Start taking quizzes to see your results here.</p>
                            <a href="quiz.php" class="btn btn-primary">
                                <i class="fas fa-brain"></i> Take Your First Quiz
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
</body>
</html>
