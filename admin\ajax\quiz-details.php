<?php
/**
 * AJAX endpoint for quiz session details
 */

require_once '../../config/database.php';

// Check if admin is logged in
requireLogin('admin');

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo '<div class="error">Invalid quiz session ID</div>';
    exit;
}

$quizId = (int)$_GET['id'];

try {
    // Get quiz session details
    $quiz = fetchOne("
        SELECT qs.*, s.first_name, s.last_name, s.student_id,
               d.name as department_name, al.level_name
        FROM quiz_sessions qs
        JOIN students s ON qs.student_id = s.id
        JOIN departments d ON qs.department_id = d.id
        JOIN academic_levels al ON qs.academic_level_id = al.id
        WHERE qs.id = :id
    ", ['id' => $quizId]);

    if (!$quiz) {
        echo '<div class="error">Quiz session not found</div>';
        exit;
    }

    // Get quiz answers if available
    $answers = fetchAll("
        SELECT qa.*, q.question_text, q.option_a, q.option_b, q.option_c, q.option_d, q.correct_answer
        FROM quiz_answers qa
        JOIN questions q ON qa.question_id = q.id
        WHERE qa.quiz_session_id = :id
        ORDER BY qa.question_number
    ", ['id' => $quizId]);

    // Calculate performance metrics
    $totalQuestions = $quiz['total_questions'];
    $score = $quiz['score'];
    $percentage = $totalQuestions > 0 ? ($score / $totalQuestions) * 100 : 0;
    $duration = $quiz['completed_at'] ? 
        (strtotime($quiz['completed_at']) - strtotime($quiz['started_at'])) : 
        (time() - strtotime($quiz['started_at']));

} catch (Exception $e) {
    echo '<div class="error">Error loading quiz details: ' . htmlspecialchars($e->getMessage()) . '</div>';
    exit;
}
?>

<div class="quiz-details">
    <!-- Quiz Header -->
    <div class="quiz-header">
        <div class="quiz-info">
            <h2><?php echo htmlspecialchars($quiz['subject_name']); ?></h2>
            <div class="quiz-meta">
                <span class="meta-item">
                    <i class="fas fa-user"></i>
                    <?php echo htmlspecialchars($quiz['first_name'] . ' ' . $quiz['last_name']); ?>
                    (<?php echo htmlspecialchars($quiz['student_id']); ?>)
                </span>
                <span class="meta-item">
                    <i class="fas fa-graduation-cap"></i>
                    <?php echo htmlspecialchars($quiz['department_name'] . ' - ' . $quiz['level_name']); ?>
                </span>
                <span class="meta-item">
                    <i class="fas fa-calendar"></i>
                    <?php echo date('M d, Y H:i', strtotime($quiz['started_at'])); ?>
                </span>
                <?php if ($quiz['completed_at']): ?>
                <span class="meta-item">
                    <i class="fas fa-clock"></i>
                    <?php echo gmdate('H:i:s', $duration); ?> duration
                </span>
                <?php endif; ?>
            </div>
        </div>
        <div class="quiz-score">
            <?php 
            $scoreClass = $percentage >= 70 ? 'excellent' : ($percentage >= 50 ? 'good' : 'poor');
            ?>
            <div class="score-circle <?php echo $scoreClass; ?>">
                <span class="score-number"><?php echo round($percentage); ?>%</span>
                <span class="score-fraction"><?php echo $score; ?>/<?php echo $totalQuestions; ?></span>
            </div>
        </div>
    </div>

    <!-- Performance Summary -->
    <div class="performance-summary">
        <div class="summary-item">
            <div class="summary-icon correct">
                <i class="fas fa-check"></i>
            </div>
            <div class="summary-content">
                <h3><?php echo $score; ?></h3>
                <p>Correct Answers</p>
            </div>
        </div>
        
        <div class="summary-item">
            <div class="summary-icon incorrect">
                <i class="fas fa-times"></i>
            </div>
            <div class="summary-content">
                <h3><?php echo $totalQuestions - $score; ?></h3>
                <p>Incorrect Answers</p>
            </div>
        </div>
        
        <div class="summary-item">
            <div class="summary-icon total">
                <i class="fas fa-list"></i>
            </div>
            <div class="summary-content">
                <h3><?php echo $totalQuestions; ?></h3>
                <p>Total Questions</p>
            </div>
        </div>
        
        <div class="summary-item">
            <div class="summary-icon time">
                <i class="fas fa-stopwatch"></i>
            </div>
            <div class="summary-content">
                <h3><?php echo gmdate('i:s', $duration); ?></h3>
                <p>Time Taken</p>
            </div>
        </div>
    </div>

    <!-- Quiz Status -->
    <div class="quiz-status">
        <div class="status-item">
            <span class="status-label">Status:</span>
            <span class="status-value <?php echo $quiz['completed_at'] ? 'completed' : 'in-progress'; ?>">
                <i class="fas fa-<?php echo $quiz['completed_at'] ? 'check-circle' : 'clock'; ?>"></i>
                <?php echo $quiz['completed_at'] ? 'Completed' : 'In Progress'; ?>
            </span>
        </div>
        
        <div class="status-item">
            <span class="status-label">Performance:</span>
            <span class="status-value performance-<?php echo $scoreClass; ?>">
                <i class="fas fa-<?php echo $percentage >= 70 ? 'star' : ($percentage >= 50 ? 'thumbs-up' : 'exclamation-triangle'); ?>"></i>
                <?php 
                if ($percentage >= 70) echo 'Excellent';
                elseif ($percentage >= 50) echo 'Good';
                else echo 'Needs Improvement';
                ?>
            </span>
        </div>
    </div>

    <!-- Detailed Answers (if available) -->
    <?php if (!empty($answers)): ?>
    <div class="detailed-answers">
        <h3><i class="fas fa-list-alt"></i> Question by Question Analysis</h3>
        <div class="answers-list">
            <?php foreach ($answers as $answer): ?>
                <div class="answer-item <?php echo $answer['is_correct'] ? 'correct' : 'incorrect'; ?>">
                    <div class="question-header">
                        <span class="question-number">Q<?php echo $answer['question_number']; ?></span>
                        <span class="answer-status">
                            <i class="fas fa-<?php echo $answer['is_correct'] ? 'check-circle' : 'times-circle'; ?>"></i>
                            <?php echo $answer['is_correct'] ? 'Correct' : 'Incorrect'; ?>
                        </span>
                    </div>
                    
                    <div class="question-content">
                        <p class="question-text"><?php echo htmlspecialchars($answer['question_text']); ?></p>
                        
                        <div class="options">
                            <?php 
                            $options = ['A' => $answer['option_a'], 'B' => $answer['option_b'], 'C' => $answer['option_c'], 'D' => $answer['option_d']];
                            foreach ($options as $key => $option): 
                                $isSelected = $answer['selected_answer'] === $key;
                                $isCorrect = $answer['correct_answer'] === $key;
                                $optionClass = '';
                                if ($isCorrect) $optionClass .= ' correct-option';
                                if ($isSelected && !$isCorrect) $optionClass .= ' selected-wrong';
                                if ($isSelected && $isCorrect) $optionClass .= ' selected-correct';
                            ?>
                                <div class="option<?php echo $optionClass; ?>">
                                    <span class="option-letter"><?php echo $key; ?></span>
                                    <span class="option-text"><?php echo htmlspecialchars($option); ?></span>
                                    <?php if ($isSelected): ?>
                                        <i class="fas fa-arrow-left selected-indicator"></i>
                                    <?php endif; ?>
                                    <?php if ($isCorrect): ?>
                                        <i class="fas fa-check correct-indicator"></i>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Action Buttons -->
    <div class="quiz-actions">
        <button class="btn btn-primary" onclick="viewStudentProgress(<?php echo $quiz['student_id']; ?>)">
            <i class="fas fa-user"></i>
            View Student Progress
        </button>
        <button class="btn btn-info" onclick="exportQuizReport(<?php echo $quiz['id']; ?>)">
            <i class="fas fa-download"></i>
            Export Report
        </button>
        <button class="btn btn-secondary" onclick="closeModal('quizDetailsModal')">
            <i class="fas fa-times"></i>
            Close
        </button>
    </div>
</div>

<style>
.quiz-details {
    max-width: 100%;
}

.quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.quiz-info h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 1rem;
}

.quiz-meta {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #718096;
}

.meta-item i {
    color: #667eea;
    width: 16px;
}

.score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    position: relative;
}

.score-circle.excellent {
    background: linear-gradient(135deg, #48bb78, #38a169);
}

.score-circle.good {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
}

.score-circle.poor {
    background: linear-gradient(135deg, #e53e3e, #c53030);
}

.score-number {
    font-size: 2rem;
    font-weight: 800;
}

.score-fraction {
    font-size: 0.875rem;
    opacity: 0.9;
}

.performance-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.summary-item {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.summary-icon.correct { background: linear-gradient(135deg, #48bb78, #38a169); }
.summary-icon.incorrect { background: linear-gradient(135deg, #e53e3e, #c53030); }
.summary-icon.total { background: linear-gradient(135deg, #4299e1, #3182ce); }
.summary-icon.time { background: linear-gradient(135deg, #9f7aea, #805ad5); }

.quiz-status {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.status-label {
    font-weight: 600;
    color: #4a5568;
}

.status-value {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
}

.status-value.completed { color: #48bb78; }
.status-value.in-progress { color: #ed8936; }
.status-value.performance-excellent { color: #48bb78; }
.status-value.performance-good { color: #ed8936; }
.status-value.performance-poor { color: #e53e3e; }

.detailed-answers h3 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    color: #1a202c;
    font-weight: 600;
}

.answer-item {
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    margin-bottom: 1rem;
    overflow: hidden;
}

.answer-item.correct {
    border-color: #48bb78;
    background: #f0fff4;
}

.answer-item.incorrect {
    border-color: #e53e3e;
    background: #fef5f5;
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.question-number {
    font-weight: 700;
    color: #1a202c;
}

.answer-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
}

.answer-item.correct .answer-status {
    color: #48bb78;
}

.answer-item.incorrect .answer-status {
    color: #e53e3e;
}

.question-content {
    padding: 1.5rem;
}

.question-text {
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.option {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    position: relative;
}

.option.correct-option {
    border-color: #48bb78;
    background: #f0fff4;
}

.option.selected-wrong {
    border-color: #e53e3e;
    background: #fef5f5;
}

.option.selected-correct {
    border-color: #48bb78;
    background: #f0fff4;
}

.option-letter {
    width: 24px;
    height: 24px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.option-text {
    flex: 1;
}

.selected-indicator {
    color: #667eea;
    margin-left: auto;
}

.correct-indicator {
    color: #48bb78;
    margin-left: auto;
}

.quiz-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    padding-top: 1.5rem;
    border-top: 1px solid #e2e8f0;
}

.btn-secondary {
    background: #718096;
    color: white;
}

.btn-secondary:hover {
    background: #4a5568;
}

@media (max-width: 768px) {
    .quiz-header {
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .quiz-status {
        flex-direction: column;
        gap: 1rem;
    }
    
    .performance-summary {
        grid-template-columns: 1fr 1fr;
    }
}
</style>
