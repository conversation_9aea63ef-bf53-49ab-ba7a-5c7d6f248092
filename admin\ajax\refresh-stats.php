<?php
/**
 * AJAX endpoint for refreshing dashboard statistics
 */

require_once '../../config/database.php';

// Check if admin is logged in
requireLogin('admin');

header('Content-Type: application/json');

try {
    // Get updated dashboard statistics
    $stats = [
        'total_students' => fetchOne("SELECT COUNT(*) as count FROM students")['count'] ?? 0,
        'approved_students' => fetchOne("SELECT COUNT(*) as count FROM students WHERE is_approved = 1")['count'] ?? 0,
        'pending_approvals' => fetchOne("SELECT COUNT(*) as count FROM students WHERE is_approved = 0")['count'] ?? 0,
        'total_departments' => fetchOne("SELECT COUNT(*) as count FROM departments")['count'] ?? 0,
        'total_subjects' => fetchOne("SELECT COUNT(*) as count FROM subjects")['count'] ?? 0,
        'total_questions' => fetchOne("SELECT COUNT(*) as count FROM questions")['count'] ?? 0,
        'active_sessions' => fetchOne("SELECT COUNT(*) as count FROM user_sessions WHERE is_active = 1 AND user_type = 'student' AND expires_at > NOW()")['count'] ?? 0,
        'total_quizzes_today' => fetchOne("SELECT COUNT(*) as count FROM quiz_sessions WHERE DATE(created_at) = CURDATE()")['count'] ?? 0
    ];

    // Get additional real-time data
    $additionalData = [
        'recent_registrations_count' => fetchOne("SELECT COUNT(*) as count FROM students WHERE DATE(created_at) = CURDATE()")['count'] ?? 0,
        'quiz_sessions_last_hour' => fetchOne("SELECT COUNT(*) as count FROM quiz_sessions WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)")['count'] ?? 0,
        'avg_quiz_score_today' => fetchOne("SELECT AVG(score/total_questions*100) as avg_score FROM quiz_sessions WHERE DATE(created_at) = CURDATE()")['avg_score'] ?? 0
    ];

    echo json_encode([
        'success' => true,
        'stats' => $stats,
        'additional' => $additionalData,
        'timestamp' => time()
    ]);

} catch (Exception $e) {
    error_log("Stats refresh error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Failed to refresh statistics',
        'timestamp' => time()
    ]);
}
?>
