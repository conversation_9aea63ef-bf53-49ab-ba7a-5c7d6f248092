<?php
/**
 * Admin Dashboard for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Require admin login
requireLogin('admin');

// Get admin info
$adminId = $_SESSION['user_id'];
$admin = fetchOne("SELECT * FROM admins WHERE id = :id", ['id' => $adminId]);

// Get dashboard statistics with error handling
try {
    $stats = [
        'total_students' => fetchOne("SELECT COUNT(*) as count FROM students")['count'] ?? 0,
        'approved_students' => fetchOne("SELECT COUNT(*) as count FROM students WHERE is_approved = 1")['count'] ?? 0,
        'pending_approvals' => fetchOne("SELECT COUNT(*) as count FROM students WHERE is_approved = 0")['count'] ?? 0,
        'total_departments' => fetchOne("SELECT COUNT(*) as count FROM departments")['count'] ?? 0,
        'total_subjects' => fetchOne("SELECT COUNT(*) as count FROM subjects")['count'] ?? 0,
        'total_questions' => fetchOne("SELECT COUNT(*) as count FROM questions")['count'] ?? 0,
        'active_sessions' => fetchOne("SELECT COUNT(*) as count FROM user_sessions WHERE is_active = 1 AND user_type = 'student' AND expires_at > NOW()")['count'] ?? 0,
        'total_quizzes_today' => fetchOne("SELECT COUNT(*) as count FROM quiz_sessions WHERE DATE(created_at) = CURDATE()")['count'] ?? 0
    ];

    // Get recent student registrations
    $recentStudents = fetchAll("
        SELECT s.*, d.name as department_name, al.level_name
        FROM students s
        JOIN departments d ON s.department_id = d.id
        JOIN academic_levels al ON s.academic_level_id = al.id
        ORDER BY s.created_at DESC
        LIMIT 8
    ");

    // Get departments with student counts
    $departmentStats = fetchAll("
        SELECT d.name, d.code, COUNT(s.id) as student_count,
               COUNT(CASE WHEN s.is_approved = 1 THEN 1 END) as approved_count
        FROM departments d
        LEFT JOIN students s ON d.id = s.department_id
        GROUP BY d.id, d.name, d.code
        ORDER BY student_count DESC
        LIMIT 10
    ");

    // Get recent quiz activity
    $recentQuizzes = fetchAll("
        SELECT qs.*, s.first_name, s.last_name, s.student_id,
               COALESCE(sub.name, 'General Quiz') as subject_name,
               qs.score, qs.total_questions
        FROM quiz_sessions qs
        JOIN students s ON qs.student_id = s.id
        LEFT JOIN subjects sub ON qs.subject_id = sub.id
        ORDER BY qs.created_at DESC
        LIMIT 10
    ");

} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $stats = [
        'total_students' => 0,
        'approved_students' => 0,
        'pending_approvals' => 0,
        'total_departments' => 0,
        'total_subjects' => 0,
        'total_questions' => 0,
        'active_sessions' => 0,
        'total_quizzes_today' => 0
    ];
    $recentStudents = [];
    $departmentStats = [];
    $recentQuizzes = [];
}

$pageTitle = 'Admin Dashboard';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Admin Panel</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>

            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item active">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="students.php" class="nav-item">
                    <i class="fas fa-users"></i>
                    <span>Students</span>
                    <?php if ($stats['pending_approvals'] > 0): ?>
                        <span class="badge"><?php echo $stats['pending_approvals']; ?></span>
                    <?php endif; ?>
                </a>
                <a href="departments.php" class="nav-item">
                    <i class="fas fa-building"></i>
                    <span>Departments</span>
                </a>
                <a href="subjects.php" class="nav-item">
                    <i class="fas fa-book"></i>
                    <span>Subjects</span>
                </a>
                <a href="questions.php" class="nav-item">
                    <i class="fas fa-question-circle"></i>
                    <span>Questions</span>
                </a>
                <a href="generate-questions.php" class="nav-item">
                    <i class="fas fa-magic"></i>
                    <span>Generate Questions</span>
                </a>
                <a href="rewards.php" class="nav-item">
                    <i class="fas fa-trophy"></i>
                    <span>Rewards</span>
                </a>
                <a href="analytics.php" class="nav-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>Analytics</span>
                </a>
                <a href="settings.php" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>

                <div class="nav-divider"></div>

                <a href="../index.php" class="nav-item nav-secondary">
                    <i class="fas fa-home"></i>
                    <span>Back to Site</span>
                </a>
                <a href="logout.php" class="nav-item nav-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>

            <div class="sidebar-footer">
                <div class="admin-profile">
                    <div class="admin-avatar">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="admin-info">
                        <span class="admin-name"><?php echo htmlspecialchars($admin['full_name'] ?? 'Admin'); ?></span>
                        <span class="admin-role">Administrator</span>
                    </div>
                </div>
                <a href="logout.php" class="nav-item logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="content-header">
                <div class="header-left">
                    <h1>Dashboard Overview</h1>
                    <p>Welcome back, <?php echo htmlspecialchars($admin['full_name'] ?? $_SESSION['username']); ?>! Here's what's happening today.</p>
                </div>
                <div class="header-right">
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="location.href='students.php'">
                            <i class="fas fa-plus"></i>
                            Manage Students
                        </button>
                        <div class="header-stats">
                            <span class="stat-item">
                                <i class="fas fa-calendar"></i>
                                <?php echo date('M d, Y'); ?>
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-clock"></i>
                                <?php echo date('H:i'); ?>
                            </span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="students.php" class="action-card students">
                    <div class="action-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="action-content">
                        <h3>Manage Students</h3>
                        <p>View, approve, and manage student accounts</p>
                        <?php if ($stats['pending_approvals'] > 0): ?>
                            <span class="action-badge"><?php echo $stats['pending_approvals']; ?> pending</span>
                        <?php endif; ?>
                    </div>
                    <div class="action-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </a>

                <a href="departments.php" class="action-card departments">
                    <div class="action-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="action-content">
                        <h3>Departments</h3>
                        <p>Manage departments and courses</p>
                        <span class="action-badge"><?php echo $stats['total_departments']; ?> departments</span>
                    </div>
                    <div class="action-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </a>

                <a href="subjects.php" class="action-card subjects">
                    <div class="action-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="action-content">
                        <h3>Subjects</h3>
                        <p>Add or remove course subjects</p>
                        <span class="action-badge"><?php echo $stats['total_subjects']; ?> subjects</span>
                    </div>
                    <div class="action-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </a>

                <a href="questions.php" class="action-card questions">
                    <div class="action-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="action-content">
                        <h3>Questions</h3>
                        <p>Manage quiz questions and content</p>
                        <span class="action-badge"><?php echo $stats['total_questions']; ?> questions</span>
                    </div>
                    <div class="action-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </a>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card primary" onclick="location.href='students.php'">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['total_students']); ?></h3>
                        <p>Total Students</p>
                        <span class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <?php echo number_format($stats['approved_students']); ?> approved
                        </span>
                    </div>
                </div>

                <div class="stat-card warning" onclick="location.href='students.php?filter=pending'">
                    <div class="stat-icon">
                        <i class="fas fa-user-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['pending_approvals']); ?></h3>
                        <p>Pending Approvals</p>
                        <span class="stat-change">
                            <i class="fas fa-clock"></i>
                            Requires attention
                        </span>
                    </div>
                </div>

                <div class="stat-card success" onclick="location.href='departments.php'">
                    <div class="stat-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['total_departments']); ?></h3>
                        <p>Departments</p>
                        <span class="stat-change">
                            <i class="fas fa-graduation-cap"></i>
                            Active programs
                        </span>
                    </div>
                </div>

                <div class="stat-card info" onclick="location.href='subjects.php'">
                    <div class="stat-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['total_subjects']); ?></h3>
                        <p>Course Subjects</p>
                        <span class="stat-change">
                            <i class="fas fa-plus"></i>
                            Add new subjects
                        </span>
                    </div>
                </div>

                <div class="stat-card purple" onclick="location.href='analytics.php'">
                    <div class="stat-icon">
                        <i class="fas fa-users-cog"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['active_sessions']); ?></h3>
                        <p>Active Students</p>
                        <span class="stat-change">
                            <i class="fas fa-wifi"></i>
                            Online now
                        </span>
                    </div>
                </div>

                <div class="stat-card orange" onclick="location.href='analytics.php'">
                    <div class="stat-icon">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['total_quizzes_today']); ?></h3>
                        <p>Quizzes Today</p>
                        <span class="stat-change">
                            <i class="fas fa-chart-line"></i>
                            Daily activity
                        </span>
                    </div>
                </div>
            </div>

            <!-- Content Grid -->
            <div class="content-grid">
                <!-- Recent Students -->
                <div class="content-card">
                    <div class="card-header">
                        <h3><i class="fas fa-user-plus"></i> Recent Student Registrations</h3>
                        <a href="students.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-eye"></i>
                            View All Students
                        </a>
                    </div>
                    <div class="card-content">
                        <?php if (empty($recentStudents)): ?>
                            <div class="empty-state">
                                <i class="fas fa-user-plus"></i>
                                <h4>No recent registrations</h4>
                                <p>New student registrations will appear here</p>
                                <a href="students.php" class="btn btn-outline">Manage Students</a>
                            </div>
                        <?php else: ?>
                            <div class="student-list">
                                <?php foreach ($recentStudents as $student): ?>
                                    <div class="student-item" onclick="viewStudentProgress('<?php echo $student['id']; ?>')">
                                        <div class="student-avatar">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="student-info">
                                            <h4><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h4>
                                            <p class="student-id"><?php echo htmlspecialchars($student['student_id']); ?></p>
                                            <p class="student-dept"><?php echo htmlspecialchars($student['department_name'] . ' - ' . $student['level_name']); ?></p>
                                            <span class="student-date">
                                                <i class="fas fa-calendar"></i>
                                                <?php echo date('M d, Y', strtotime($student['created_at'])); ?>
                                            </span>
                                        </div>
                                        <div class="student-actions">
                                            <div class="student-status">
                                                <?php if ($student['is_approved']): ?>
                                                    <span class="status approved">
                                                        <i class="fas fa-check-circle"></i>
                                                        Approved
                                                    </span>
                                                <?php else: ?>
                                                    <span class="status pending">
                                                        <i class="fas fa-clock"></i>
                                                        Pending
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <button class="btn-icon" onclick="event.stopPropagation(); viewStudentProgress('<?php echo $student['id']; ?>')" title="View Progress">
                                                <i class="fas fa-chart-line"></i>
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Department Overview -->
                <div class="content-card">
                    <div class="card-header">
                        <h3><i class="fas fa-building"></i> Department Overview</h3>
                        <a href="departments.php" class="btn btn-sm btn-success">
                            <i class="fas fa-plus"></i>
                            Manage Departments
                        </a>
                    </div>
                    <div class="card-content">
                        <?php if (empty($departmentStats)): ?>
                            <div class="empty-state">
                                <i class="fas fa-building"></i>
                                <h4>No department data</h4>
                                <p>Department statistics will appear here</p>
                                <a href="departments.php" class="btn btn-outline">Setup Departments</a>
                            </div>
                        <?php else: ?>
                            <div class="department-list">
                                <?php foreach ($departmentStats as $dept): ?>
                                    <div class="department-item" onclick="location.href='students.php?department=<?php echo urlencode($dept['code']); ?>'">
                                        <div class="dept-icon">
                                            <i class="fas fa-graduation-cap"></i>
                                        </div>
                                        <div class="dept-info">
                                            <h4><?php echo htmlspecialchars($dept['name']); ?></h4>
                                            <p class="dept-code"><?php echo htmlspecialchars($dept['code']); ?></p>
                                        </div>
                                        <div class="dept-stats">
                                            <div class="stat-item">
                                                <span class="stat-number"><?php echo $dept['student_count']; ?></span>
                                                <span class="stat-label">Total</span>
                                            </div>
                                            <div class="stat-item">
                                                <span class="stat-number approved"><?php echo $dept['approved_count']; ?></span>
                                                <span class="stat-label">Approved</span>
                                            </div>
                                        </div>
                                        <div class="dept-arrow">
                                            <i class="fas fa-chevron-right"></i>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Quiz Activity -->
                <div class="content-card full-width">
                    <div class="card-header">
                        <h3><i class="fas fa-clipboard-list"></i> Recent Quiz Activity</h3>
                        <a href="analytics.php" class="btn btn-sm btn-info">
                            <i class="fas fa-chart-bar"></i>
                            View Analytics
                        </a>
                    </div>
                    <div class="card-content">
                        <?php if (empty($recentQuizzes)): ?>
                            <div class="empty-state">
                                <i class="fas fa-clipboard-list"></i>
                                <h4>No recent quiz activity</h4>
                                <p>Student quiz attempts will appear here</p>
                            </div>
                        <?php else: ?>
                            <div class="quiz-activity-table">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>Student</th>
                                            <th>Subject</th>
                                            <th>Score</th>
                                            <th>Questions</th>
                                            <th>Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentQuizzes as $quiz): ?>
                                            <tr onclick="viewQuizDetails('<?php echo $quiz['id']; ?>')">
                                                <td>
                                                    <div class="student-info">
                                                        <strong><?php echo htmlspecialchars($quiz['first_name'] . ' ' . $quiz['last_name']); ?></strong>
                                                        <small><?php echo htmlspecialchars($quiz['student_id']); ?></small>
                                                    </div>
                                                </td>
                                                <td><?php echo htmlspecialchars($quiz['subject_name']); ?></td>
                                                <td>
                                                    <span class="score-badge <?php echo ($quiz['score'] / $quiz['total_questions']) >= 0.7 ? 'good' : (($quiz['score'] / $quiz['total_questions']) >= 0.5 ? 'average' : 'poor'); ?>">
                                                        <?php echo $quiz['score']; ?>/<?php echo $quiz['total_questions']; ?>
                                                        (<?php echo round(($quiz['score'] / $quiz['total_questions']) * 100); ?>%)
                                                    </span>
                                                </td>
                                                <td><?php echo $quiz['total_questions']; ?></td>
                                                <td><?php echo date('M d, H:i', strtotime($quiz['created_at'])); ?></td>
                                                <td>
                                                    <button class="btn-icon" onclick="event.stopPropagation(); viewQuizDetails('<?php echo $quiz['id']; ?>')" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Student Progress Modal -->
    <div id="studentProgressModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Student Progress</h3>
                <span class="close" onclick="closeModal('studentProgressModal')">&times;</span>
            </div>
            <div class="modal-body" id="studentProgressContent">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    Loading student progress...
                </div>
            </div>
        </div>
    </div>

    <!-- Quiz Details Modal -->
    <div id="quizDetailsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Quiz Details</h3>
                <span class="close" onclick="closeModal('quizDetailsModal')">&times;</span>
            </div>
            <div class="modal-body" id="quizDetailsContent">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    Loading quiz details...
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/admin-dashboard.js"></script>
    <script>
        // Dashboard specific functions
        function viewStudentProgress(studentId) {
            const modal = document.getElementById('studentProgressModal');
            const content = document.getElementById('studentProgressContent');

            modal.style.display = 'block';
            content.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading student progress...</div>';

            fetch(`ajax/student-progress.php?id=${studentId}`)
                .then(response => response.text())
                .then(data => {
                    content.innerHTML = data;
                })
                .catch(error => {
                    content.innerHTML = '<div class="error">Error loading student progress. Please try again.</div>';
                });
        }

        function viewQuizDetails(quizId) {
            const modal = document.getElementById('quizDetailsModal');
            const content = document.getElementById('quizDetailsContent');

            modal.style.display = 'block';
            content.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading quiz details...</div>';

            fetch(`ajax/quiz-details.php?id=${quizId}`)
                .then(response => response.text())
                .then(data => {
                    content.innerHTML = data;
                })
                .catch(error => {
                    content.innerHTML = '<div class="error">Error loading quiz details. Please try again.</div>';
                });
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Auto-refresh stats every 30 seconds
        setInterval(() => {
            fetch('ajax/refresh-stats.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update stat cards
                        Object.keys(data.stats).forEach(key => {
                            const element = document.querySelector(`[data-stat="${key}"]`);
                            if (element) {
                                element.textContent = new Intl.NumberFormat().format(data.stats[key]);
                            }
                        });
                    }
                })
                .catch(error => console.log('Stats refresh failed:', error));
        }, 30000);
    </script>
</body>
</html>
