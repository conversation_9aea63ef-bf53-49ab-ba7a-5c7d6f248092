<?php
/**
 * Get Department Details AJAX Endpoint
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../../config/database.php';

// Set JSON header
header('Content-Type: application/json');

try {
    // Require admin login
    requireLogin('admin');
    
    // Get department ID
    $departmentId = (int)($_GET['id'] ?? 0);
    
    if (!$departmentId) {
        throw new Exception('Department ID is required');
    }
    
    // Get department details
    $department = fetchOne("
        SELECT d.*,
               (SELECT COUNT(*) FROM students WHERE department_id = d.id) as student_count,
               (SELECT COUNT(*) FROM subjects WHERE department_id = d.id) as subject_count,
               (SELECT COUNT(*) FROM students WHERE department_id = d.id AND is_approved = 1) as approved_students
        FROM departments d
        WHERE d.id = :id
    ", ['id' => $departmentId]);
    
    if (!$department) {
        throw new Exception('Department not found');
    }
    
    // Get students in this department
    $students = fetchAll("
        SELECT s.*, al.level_name,
               (SELECT COUNT(*) FROM quiz_sessions WHERE student_id = s.id) as quiz_count,
               (SELECT AVG(score/total_questions*100) FROM quiz_sessions WHERE student_id = s.id AND status = 'completed') as avg_score
        FROM students s
        JOIN academic_levels al ON s.academic_level_id = al.id
        WHERE s.department_id = :id
        ORDER BY s.created_at DESC
        LIMIT 10
    ", ['id' => $departmentId]);
    
    // Get academic levels in this department with question counts
    $levels = fetchAll("
        SELECT al.*,
               (SELECT COUNT(*) FROM questions WHERE department_id = :dept_id AND academic_level_id = al.id) as question_count
        FROM academic_levels al
        ORDER BY al.level_name
    ", ['dept_id' => $departmentId]);
    
    // Get recent quiz activity for this department
    $recentQuizzes = fetchAll("
        SELECT qs.*, st.first_name, st.last_name, st.student_id,
               d.name as department_name, al.level_name
        FROM quiz_sessions qs
        JOIN students st ON qs.student_id = st.id
        JOIN departments d ON qs.department_id = d.id
        JOIN academic_levels al ON qs.academic_level_id = al.id
        WHERE qs.department_id = :id
        ORDER BY qs.created_at DESC
        LIMIT 10
    ", ['id' => $departmentId]);
    
    // Calculate department statistics
    $stats = [
        'total_students' => (int)$department['student_count'],
        'approved_students' => (int)$department['approved_students'],
        'pending_students' => (int)$department['student_count'] - (int)$department['approved_students'],
        'total_subjects' => (int)$department['subject_count'],
        'total_quizzes' => 0,
        'avg_score' => 0,
        'completion_rate' => 0
    ];
    
    // Get quiz statistics for this department
    $quizStats = fetchOne("
        SELECT 
            COUNT(*) as total_quizzes,
            AVG(qs.score/qs.total_questions*100) as avg_score,
            (COUNT(CASE WHEN qs.status = 'completed' THEN 1 END) / COUNT(*) * 100) as completion_rate
        FROM quiz_sessions qs
        JOIN students s ON qs.student_id = s.id
        WHERE s.department_id = :id
    ", ['id' => $departmentId]);
    
    if ($quizStats) {
        $stats['total_quizzes'] = (int)$quizStats['total_quizzes'];
        $stats['avg_score'] = round($quizStats['avg_score'] ?? 0, 1);
        $stats['completion_rate'] = round($quizStats['completion_rate'] ?? 0, 1);
    }
    
    // Get performance by academic level
    $levelPerformance = fetchAll("
        SELECT al.level_name,
               COUNT(DISTINCT s.id) as student_count,
               COUNT(qs.id) as quiz_count,
               AVG(qs.score/qs.total_questions*100) as avg_score
        FROM academic_levels al
        LEFT JOIN students s ON al.id = s.academic_level_id AND s.department_id = :id AND s.is_approved = 1
        LEFT JOIN quiz_sessions qs ON s.id = qs.student_id AND qs.status = 'completed'
        GROUP BY al.id, al.level_name
        HAVING student_count > 0
        ORDER BY al.level_name
    ", ['id' => $departmentId]);
    
    // Response data
    $response = [
        'success' => true,
        'department' => $department,
        'students' => $students,
        'subjects' => $subjects,
        'recent_quizzes' => $recentQuizzes,
        'statistics' => $stats,
        'level_performance' => $levelPerformance,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
