<?php
/**
 * Create Test Student for Testing Quiz System
 */

require_once '../config/database.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Create Test Student</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; padding: 10px; background: #d4edda; border-radius: 5px; margin: 10px 0; }
        .error { color: red; padding: 10px; background: #f8d7da; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; padding: 10px; background: #d1ecf1; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>";

echo "<h1>👨‍🎓 Create Test Student</h1>";

try {
    // Check if departments exist
    $departments = fetchAll("SELECT * FROM departments LIMIT 5");
    if (empty($departments)) {
        echo "<div class='error'>❌ No departments found. Please run the initial data setup first.</div>";
        echo "</body></html>";
        exit;
    }

    // Check if academic levels exist
    $levels = fetchAll("SELECT * FROM academic_levels LIMIT 5");
    if (empty($levels)) {
        echo "<div class='error'>❌ No academic levels found. Please run the initial data setup first.</div>";
        echo "</body></html>";
        exit;
    }

    echo "<div class='info'>📚 Available Departments:</div>";
    foreach ($departments as $dept) {
        echo "<p>- " . htmlspecialchars($dept['name']) . " (ID: " . $dept['id'] . ")</p>";
    }

    echo "<div class='info'>🎓 Available Academic Levels:</div>";
    foreach ($levels as $level) {
        echo "<p>- " . htmlspecialchars($level['level_name']) . " (ID: " . $level['id'] . ")</p>";
    }

    // Check if test student already exists
    $existingStudent = fetchOne("SELECT * FROM students WHERE student_id = 'TEST001'");
    
    if ($existingStudent) {
        echo "<div class='info'>ℹ️ Test student already exists:</div>";
        echo "<p><strong>Student ID:</strong> " . htmlspecialchars($existingStudent['student_id']) . "</p>";
        echo "<p><strong>Name:</strong> " . htmlspecialchars($existingStudent['first_name'] . ' ' . $existingStudent['last_name']) . "</p>";
        echo "<p><strong>Email:</strong> " . htmlspecialchars($existingStudent['email']) . "</p>";
        echo "<p><strong>Password:</strong> test123 (for testing)</p>";
        
        echo "<div class='success'>✅ You can use this student to test the quiz system!</div>";
        echo "<p><a href='../login.php'>🔐 Go to Login Page</a></p>";
        echo "<p><a href='quiz.php'>🧠 Go to Quiz Page (if already logged in)</a></p>";
    } else {
        // Create test student
        $password = password_hash('test123', PASSWORD_DEFAULT);
        
        $studentId = insertRecord('students', [
            'student_id' => 'TEST001',
            'first_name' => 'Test',
            'last_name' => 'Student',
            'email' => '<EMAIL>',
            'phone' => '08012345678',
            'password' => $password,
            'department_id' => $departments[0]['id'], // Use first department
            'academic_level_id' => $levels[0]['id'], // Use first level
            'is_approved' => 1,
            'registration_date' => date('Y-m-d H:i:s')
        ]);
        
        if ($studentId) {
            echo "<div class='success'>✅ Test student created successfully!</div>";
            echo "<div class='info'>📋 Student Details:</div>";
            echo "<p><strong>Student ID:</strong> TEST001</p>";
            echo "<p><strong>Name:</strong> Test Student</p>";
            echo "<p><strong>Email:</strong> <EMAIL></p>";
            echo "<p><strong>Password:</strong> test123</p>";
            echo "<p><strong>Department:</strong> " . htmlspecialchars($departments[0]['name']) . "</p>";
            echo "<p><strong>Level:</strong> " . htmlspecialchars($levels[0]['level_name']) . "</p>";
            
            echo "<div class='success'>🎉 You can now test the quiz system with this student!</div>";
            echo "<p><a href='../login.php'>🔐 Go to Login Page</a></p>";
        } else {
            echo "<div class='error'>❌ Failed to create test student</div>";
        }
    }

} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}

echo "</body></html>";
?>
