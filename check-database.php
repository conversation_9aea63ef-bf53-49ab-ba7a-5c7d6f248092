<?php
/**
 * Database Check Script for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database_name = 'lms_ogbonnaya_onu';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Database Check - AI-Powered LMS</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { color: #16a34a; background: #f0fdf4; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc2626; background: #fef2f2; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #d97706; background: #fffbeb; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0369a1; background: #f0f9ff; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .check { margin: 15px 0; padding: 10px; border-left: 4px solid #6366f1; background: #f8fafc; }
        h1 { color: #1f2937; }
        h2 { color: #374151; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f3f4f6; }
        .btn { background: #6366f1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
        .btn:hover { background: #5856eb; }
    </style>
</head>
<body>
    <h1>AI-Powered LMS Database Check</h1>
    <p>Checking database status for Ogbonnaya Onu Polytechnic, Aba</p>";

// Check 1: MySQL Connection
echo "<div class='check'><h2>1. MySQL Server Connection</h2>";
try {
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<div class='success'>✓ MySQL server is running and accessible</div>";
    
    // Get MySQL version
    $version = $pdo->query('SELECT VERSION()')->fetchColumn();
    echo "<div class='info'>MySQL Version: $version</div>";
    
} catch (PDOException $e) {
    echo "<div class='error'>✗ Cannot connect to MySQL server: " . $e->getMessage() . "</div>";
    echo "<div class='warning'>Please check if MySQL/MariaDB is running and credentials are correct.</div>";
    echo "</div></body></html>";
    exit;
}
echo "</div>";

// Check 2: Database Existence
echo "<div class='check'><h2>2. Database Existence</h2>";
try {
    $databases = $pdo->query("SHOW DATABASES")->fetchAll(PDO::FETCH_COLUMN);
    
    if (in_array($database_name, $databases)) {
        echo "<div class='success'>✓ Database '$database_name' exists</div>";
        
        // Connect to the specific database
        $pdo = new PDO("mysql:host=$host;dbname=$database_name", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
    } else {
        echo "<div class='error'>✗ Database '$database_name' does not exist</div>";
        echo "<div class='warning'>Available databases: " . implode(', ', $databases) . "</div>";
        echo "<a href='setup-database.php' class='btn'>Create Database</a>";
        echo "</div></body></html>";
        exit;
    }
} catch (PDOException $e) {
    echo "<div class='error'>✗ Error checking databases: " . $e->getMessage() . "</div>";
    echo "</div></body></html>";
    exit;
}
echo "</div>";

// Check 3: Tables
echo "<div class='check'><h2>3. Database Tables</h2>";
try {
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    $expectedTables = [
        'admins', 'students', 'departments', 'academic_levels', 'subjects',
        'questions', 'quiz_sessions', 'quiz_answers', 'rewards', 'student_rewards',
        'user_sessions', 'student_security_answers', 'security_questions',
        'student_progress', 'system_settings'
    ];
    
    echo "<div class='success'>✓ Found " . count($tables) . " tables</div>";
    
    echo "<table>";
    echo "<tr><th>Expected Table</th><th>Status</th><th>Records</th></tr>";
    
    foreach ($expectedTables as $table) {
        echo "<tr>";
        echo "<td>$table</td>";
        
        if (in_array($table, $tables)) {
            echo "<td style='color: #16a34a;'>✓ Exists</td>";
            
            // Count records
            try {
                $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
                echo "<td>$count records</td>";
            } catch (PDOException $e) {
                echo "<td style='color: #dc2626;'>Error counting</td>";
            }
        } else {
            echo "<td style='color: #dc2626;'>✗ Missing</td>";
            echo "<td>-</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
    
    $missingTables = array_diff($expectedTables, $tables);
    if (!empty($missingTables)) {
        echo "<div class='warning'>Missing tables: " . implode(', ', $missingTables) . "</div>";
        echo "<a href='setup-database.php' class='btn'>Recreate Tables</a>";
    }
    
} catch (PDOException $e) {
    echo "<div class='error'>✗ Error checking tables: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Check 4: Admin Accounts
echo "<div class='check'><h2>4. Admin Accounts</h2>";
try {
    if (in_array('admins', $tables)) {
        $adminCount = $pdo->query("SELECT COUNT(*) FROM admins")->fetchColumn();
        
        if ($adminCount > 0) {
            echo "<div class='success'>✓ Found $adminCount admin account(s)</div>";
            
            // Show admin details (without passwords)
            $admins = $pdo->query("SELECT id, username, email, full_name, created_at FROM admins ORDER BY created_at")->fetchAll();
            
            echo "<table>";
            echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Full Name</th><th>Created</th></tr>";
            foreach ($admins as $admin) {
                echo "<tr>";
                echo "<td>{$admin['id']}</td>";
                echo "<td>{$admin['username']}</td>";
                echo "<td>{$admin['email']}</td>";
                echo "<td>{$admin['full_name']}</td>";
                echo "<td>{$admin['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
        } else {
            echo "<div class='warning'>⚠ No admin accounts found</div>";
            echo "<a href='setup-admin.php' class='btn'>Create Admin Account</a>";
        }
    } else {
        echo "<div class='error'>✗ Admins table not found</div>";
    }
} catch (PDOException $e) {
    echo "<div class='error'>✗ Error checking admin accounts: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Check 5: Student Accounts
echo "<div class='check'><h2>5. Student Accounts</h2>";
try {
    if (in_array('students', $tables)) {
        $studentCount = $pdo->query("SELECT COUNT(*) FROM students")->fetchColumn();
        $approvedCount = $pdo->query("SELECT COUNT(*) FROM students WHERE is_approved = 1")->fetchColumn();
        $pendingCount = $studentCount - $approvedCount;
        
        echo "<div class='info'>Total Students: $studentCount</div>";
        echo "<div class='success'>Approved: $approvedCount</div>";
        if ($pendingCount > 0) {
            echo "<div class='warning'>Pending Approval: $pendingCount</div>";
        }
        
    } else {
        echo "<div class='error'>✗ Students table not found</div>";
    }
} catch (PDOException $e) {
    echo "<div class='error'>✗ Error checking student accounts: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Check 6: System Status
echo "<div class='check'><h2>6. System Status</h2>";

$allGood = true;

// Check if all required tables exist
if (count(array_diff($expectedTables, $tables)) > 0) {
    $allGood = false;
}

// Check if admin exists
if (!isset($adminCount) || $adminCount == 0) {
    $allGood = false;
}

if ($allGood) {
    echo "<div class='success'>
            <h3>✓ System Ready!</h3>
            <p>Your AI-Powered LMS is properly set up and ready to use.</p>
            <a href='index.php' class='btn'>Go to Main Page</a>
            <a href='admin/login.php' class='btn'>Admin Login</a>
            <a href='student/login.php' class='btn'>Student Login</a>
          </div>";
} else {
    echo "<div class='warning'>
            <h3>⚠ Setup Required</h3>
            <p>Your system needs some setup before it's ready to use.</p>
            <a href='setup-database.php' class='btn'>Setup Database</a>
            <a href='setup-admin.php' class='btn'>Setup Admin</a>
          </div>";
}

echo "</div>";

echo "<div style='margin-top: 30px; padding: 20px; background: #f8fafc; border-radius: 10px; text-align: center;'>
        <p style='color: #6b7280; margin: 0;'>
            <strong>AI-Powered LMS</strong> for Ogbonnaya Onu Polytechnic, Aba<br>
            Database Check completed at " . date('Y-m-d H:i:s') . "
        </p>
      </div>";

echo "</body></html>";
?>
