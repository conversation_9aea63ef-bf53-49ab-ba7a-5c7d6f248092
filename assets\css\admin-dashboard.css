/* Admin Dashboard Styles - Modern Professional Design */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 25%, #b3dbff 50%, #99cfff 75%, #80c3ff 100%);
    min-height: 100vh;
    color: #2d3748;
    line-height: 1.6;
    position: relative;
    overflow-x: hidden;
}

/* Animated background elements */
body::before {
    content: '';
    position: fixed;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.05) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
    z-index: -1;
}

@keyframes float {
    0%, 100% { transform: translate(0px, 0px) rotate(0deg); }
    33% { transform: translate(30px, -30px) rotate(120deg); }
    66% { transform: translate(-20px, 20px) rotate(240deg); }
}

.admin-layout {
    display: flex;
    min-height: 100vh;
    position: relative;
    z-index: 1;
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    background: linear-gradient(180deg, #1a202c 0%, #2d3748 100%);
    color: white;
    display: flex;
    flex-direction: column;
    box-shadow: 4px 0 30px rgba(0, 0, 0, 0.15);
    position: fixed;
    height: 100vh;
    z-index: 1000;
    overflow-y: auto;
    overflow-x: hidden;
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

/* Custom scrollbar for sidebar */
.sidebar::-webkit-scrollbar {
    width: 8px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-logo {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    object-fit: cover;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.sidebar-title h3 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: #fff;
}

.sidebar-title p {
    font-size: 0.875rem;
    color: #a0aec0;
    font-weight: 400;
}

.sidebar-nav {
    flex: 1;
    padding: 1.5rem 0;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    color: #a0aec0;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    font-weight: 500;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    transform: translateX(4px);
}

.nav-item.active {
    background: linear-gradient(90deg, #667eea, #764ba2);
    color: #fff;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.nav-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #fff;
}

.nav-item i {
    font-size: 1.125rem;
    width: 20px;
    text-align: center;
}

.badge {
    background: #e53e3e;
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    margin-left: auto;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-profile {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    margin-bottom: 1rem;
}

.admin-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.125rem;
}

.admin-info {
    display: flex;
    flex-direction: column;
}

.admin-name {
    font-weight: 600;
    color: #fff;
    font-size: 0.875rem;
}

.admin-role {
    font-size: 0.75rem;
    color: #a0aec0;
}

.logout {
    color: #fed7d7 !important;
}

.logout:hover {
    background: rgba(229, 62, 62, 0.2) !important;
    color: #fff !important;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 280px;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(226, 232, 240, 0.8) 100%);
    min-height: 100vh;
    overflow-x: hidden;
    padding: 2.5rem;
    backdrop-filter: blur(10px);
    position: relative;
}

.content-header {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(240, 249, 255, 0.95) 50%, rgba(224, 242, 254, 0.9) 100%);
    padding: 3rem;
    border-bottom: 3px solid rgba(59, 130, 246, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: -2.5rem -2.5rem 2.5rem -2.5rem;
    border-radius: 0 0 30px 30px;
    box-shadow: 0 15px 45px rgba(59, 130, 246, 0.1), 0 5px 15px rgba(0, 0, 0, 0.05);
    position: relative;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    overflow: hidden;
}

.content-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8, #2563eb);
    border-radius: 30px 30px 0 0;
}

.header-left h1 {
    font-size: 2.25rem;
    font-weight: 800;
    background: linear-gradient(135deg, #1e40af, #3b82f6, #2563eb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.75rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left p {
    color: #475569;
    font-size: 1.1rem;
    font-weight: 500;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.header-stats {
    display: flex;
    gap: 1.5rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #718096;
    font-size: 0.875rem;
    font-weight: 500;
}

.stat-item i {
    color: #667eea;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.btn-success {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
}

.btn-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

.btn-icon {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 8px;
    background: #f7fafc;
    color: #718096;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-icon:hover {
    background: #667eea;
    color: white;
    transform: scale(1.1);
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    padding: 2rem 2.5rem;
}

.action-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
    border-radius: 20px;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    text-decoration: none;
    color: inherit;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.action-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border-color: transparent;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    border-radius: 20px 20px 0 0;
}

.action-card.students {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.05) 100%);
    border: 1px solid rgba(102, 126, 234, 0.15);
}

.action-card.students::before {
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.action-card.departments {
    background: linear-gradient(135deg, rgba(72, 187, 120, 0.08) 0%, rgba(56, 161, 105, 0.05) 100%);
    border: 1px solid rgba(72, 187, 120, 0.15);
}

.action-card.departments::before {
    background: linear-gradient(90deg, #48bb78, #38a169);
}

.action-card.subjects {
    background: linear-gradient(135deg, rgba(237, 137, 54, 0.08) 0%, rgba(221, 107, 32, 0.05) 100%);
    border: 1px solid rgba(237, 137, 54, 0.15);
}

.action-card.subjects::before {
    background: linear-gradient(90deg, #ed8936, #dd6b20);
}

.action-card.questions {
    background: linear-gradient(135deg, rgba(159, 122, 234, 0.08) 0%, rgba(128, 90, 213, 0.05) 100%);
    border: 1px solid rgba(159, 122, 234, 0.15);
}

.action-card.questions::before {
    background: linear-gradient(90deg, #9f7aea, #805ad5);
}

.action-icon {
    width: 70px;
    height: 70px;
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    color: white;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.students .action-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.departments .action-icon {
    background: linear-gradient(135deg, #48bb78, #38a169);
    box-shadow: 0 8px 20px rgba(72, 187, 120, 0.4);
}

.subjects .action-icon {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    box-shadow: 0 8px 20px rgba(237, 137, 54, 0.4);
}

.questions .action-icon {
    background: linear-gradient(135deg, #9f7aea, #805ad5);
    box-shadow: 0 8px 20px rgba(159, 122, 234, 0.4);
}

.action-card:hover .action-icon {
    transform: scale(1.1);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

.action-content {
    flex: 1;
}

.action-content h3 {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.5rem;
}

.action-content p {
    color: #718096;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.action-badge {
    background: #edf2f7;
    color: #4a5568;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.action-arrow {
    color: #cbd5e0;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.action-card:hover .action-arrow {
    color: #667eea;
    transform: translateX(4px);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    padding: 2rem 2.5rem;
    position: relative;
}

.stat-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(240, 249, 255, 0.95) 100%);
    border-radius: 24px;
    padding: 2.5rem;
    display: flex;
    align-items: center;
    gap: 2rem;
    box-shadow: 0 15px 45px rgba(0, 0, 0, 0.12), 0 5px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.4);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    min-height: 140px;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 24px 24px 0 0;
}

.stat-card::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    pointer-events: none;
    transition: all 0.5s ease;
}

.stat-card:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.18), 0 10px 25px rgba(0, 0, 0, 0.12);
}

.stat-card:hover::after {
    top: -30%;
    right: -30%;
    opacity: 1;
}

/* Primary Card - Blue Theme */
.stat-card.primary {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.1) 100%);
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.stat-card.primary::before {
    background: linear-gradient(90deg, #667eea, #764ba2);
}

/* Warning Card - Orange Theme */
.stat-card.warning {
    background: linear-gradient(135deg, rgba(237, 137, 54, 0.15) 0%, rgba(221, 107, 32, 0.1) 100%);
    border: 1px solid rgba(237, 137, 54, 0.2);
}

.stat-card.warning::before {
    background: linear-gradient(90deg, #ed8936, #dd6b20);
}

/* Success Card - Green Theme */
.stat-card.success {
    background: linear-gradient(135deg, rgba(72, 187, 120, 0.15) 0%, rgba(56, 161, 105, 0.1) 100%);
    border: 1px solid rgba(72, 187, 120, 0.2);
}

.stat-card.success::before {
    background: linear-gradient(90deg, #48bb78, #38a169);
}

/* Info Card - Light Blue Theme */
.stat-card.info {
    background: linear-gradient(135deg, rgba(66, 153, 225, 0.15) 0%, rgba(49, 130, 206, 0.1) 100%);
    border: 1px solid rgba(66, 153, 225, 0.2);
}

.stat-card.info::before {
    background: linear-gradient(90deg, #4299e1, #3182ce);
}

/* Purple Card - Purple Theme */
.stat-card.purple {
    background: linear-gradient(135deg, rgba(128, 90, 213, 0.15) 0%, rgba(124, 58, 237, 0.1) 100%);
    border: 1px solid rgba(128, 90, 213, 0.2);
}

.stat-card.purple::before {
    background: linear-gradient(90deg, #805ad5, #7c3aed);
}

/* Orange Card - Orange Theme */
.stat-card.orange {
    background: linear-gradient(135deg, rgba(251, 146, 60, 0.15) 0%, rgba(249, 115, 22, 0.1) 100%);
    border: 1px solid rgba(251, 146, 60, 0.2);
}

.stat-card.orange::before {
    background: linear-gradient(90deg, #fb923c, #f97316);
}

.stat-card.purple::before {
    background: linear-gradient(90deg, #9f7aea, #805ad5);
}

.stat-card.orange::before {
    background: linear-gradient(90deg, #f6ad55, #ed8936);
}

.stat-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover .stat-icon::before {
    opacity: 1;
}

.stat-card.primary .stat-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.stat-card.warning .stat-icon {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    box-shadow: 0 8px 25px rgba(237, 137, 54, 0.4);
}

.stat-card.success .stat-icon {
    background: linear-gradient(135deg, #48bb78, #38a169);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
}

.stat-card.info .stat-icon {
    background: linear-gradient(135deg, #4299e1, #3182ce);
    box-shadow: 0 8px 25px rgba(66, 153, 225, 0.4);
}

.stat-card.purple .stat-icon {
    background: linear-gradient(135deg, #805ad5, #7c3aed);
    box-shadow: 0 8px 25px rgba(128, 90, 213, 0.4);
}

.stat-card.orange .stat-icon {
    background: linear-gradient(135deg, #fb923c, #f97316);
    box-shadow: 0 8px 25px rgba(251, 146, 60, 0.4);
}

.stat-card.purple .stat-icon {
    background: linear-gradient(135deg, #9f7aea, #805ad5);
}

.stat-card.orange .stat-icon {
    background: linear-gradient(135deg, #f6ad55, #ed8936);
}

.stat-content {
    flex: 1;
}

.stat-content h3 {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1a202c;
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-content p {
    font-size: 1rem;
    color: #4a5568;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #718096;
    font-weight: 500;
}

.stat-change.positive {
    color: #48bb78;
}

.stat-change i {
    font-size: 0.75rem;
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    padding: 0 2.5rem 2rem;
}

.content-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.content-card.full-width {
    grid-column: 1 / -1;
}

.card-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8fafc;
}

.card-header h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1a202c;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-header h3 i {
    color: #667eea;
}

.card-content {
    padding: 2rem;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #718096;
}

.empty-state i {
    font-size: 3rem;
    color: #cbd5e0;
    margin-bottom: 1rem;
}

.empty-state h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.5rem;
}

.empty-state p {
    margin-bottom: 1.5rem;
}

/* Student List */
.student-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.student-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.student-item:hover {
    border-color: #667eea;
    background: #f8fafc;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.student-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.student-info {
    flex: 1;
}

.student-info h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 0.25rem;
}

.student-id {
    font-size: 0.875rem;
    color: #667eea;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.student-dept {
    font-size: 0.875rem;
    color: #718096;
    margin-bottom: 0.5rem;
}

.student-date {
    font-size: 0.75rem;
    color: #a0aec0;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.student-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status.approved {
    background: #c6f6d5;
    color: #22543d;
}

.status.pending {
    background: #fed7cc;
    color: #c53030;
}

/* Students Management Styles */
.students-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    padding: 0;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.students-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
    border-bottom: none;
    position: relative;
}

.students-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

.students-header h3 {
    font-size: 1.75rem;
    font-weight: 800;
    color: white;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 1;
}

.view-toggle {
    display: flex;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.15);
    padding: 0.5rem;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 1;
}

.btn-toggle {
    width: 44px;
    height: 44px;
    border: none;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 1.1rem;
}

.btn-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateY(-2px);
}

.btn-toggle.active {
    background: white;
    color: #667eea;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* Students Grid View */
.students-grid-view {
    display: block;
    padding: 2rem;
}

.students-grid-view.active {
    display: block;
}

.students-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(420px, 1fr));
    gap: 2rem;
}

.student-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid rgba(226, 232, 240, 0.6);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.08),
        0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
}

.student-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.student-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 8px 16px rgba(102, 126, 234, 0.2);
    border-color: rgba(102, 126, 234, 0.3);
}

.student-card:hover::before {
    opacity: 1;
}

.student-card-header {
    padding: 2rem 2rem 1.5rem 2rem;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    position: relative;
}

.student-card .student-avatar {
    width: 72px;
    height: 72px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.75rem;
    flex-shrink: 0;
    box-shadow:
        0 8px 20px rgba(102, 126, 234, 0.3),
        0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.student-card .student-avatar::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

.student-card:hover .student-avatar::before {
    opacity: 1;
    animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.student-basic-info {
    flex: 1;
    min-width: 0;
}

.student-basic-info h4 {
    font-size: 1.375rem;
    font-weight: 800;
    color: #1a202c;
    margin: 0 0 0.5rem 0;
    line-height: 1.2;
    letter-spacing: -0.025em;
}

.student-id {
    font-size: 0.875rem;
    color: #667eea;
    font-weight: 700;
    margin: 0 0 0.375rem 0;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.student-email {
    font-size: 0.875rem;
    color: #64748b;
    margin: 0;
    font-weight: 500;
}

.student-status {
    flex-shrink: 0;
    margin-top: 0.25rem;
}

.student-card-body {
    padding: 1.5rem 2rem 2rem 2rem;
}

.student-details-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.detail-item {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 1rem;
    border-radius: 12px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.detail-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.detail-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: rgba(102, 126, 234, 0.3);
}

.detail-item:hover::before {
    transform: scaleX(1);
}

.detail-label {
    font-size: 0.75rem;
    font-weight: 700;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.75px;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.detail-label::before {
    content: '';
    width: 4px;
    height: 4px;
    background: #667eea;
    border-radius: 50%;
}

.detail-value {
    font-size: 0.9375rem;
    font-weight: 700;
    color: #1e293b;
    line-height: 1.4;
}

.student-card-actions {
    padding: 1.5rem 2rem 2rem 2rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-top: 1px solid rgba(226, 232, 240, 0.5);
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    justify-content: center;
}

.btn-action {
    padding: 0.75rem 1.25rem;
    border: none;
    border-radius: 12px;
    font-size: 0.8125rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
    min-width: 100px;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.btn-action:hover::before {
    left: 100%;
}

.btn-action.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-action.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
}

.btn-action.success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(72, 187, 120, 0.4);
}

.btn-action.success:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.5);
}

.btn-action.warning {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(237, 137, 54, 0.4);
}

.btn-action.warning:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(237, 137, 54, 0.5);
}

.btn-action.secondary {
    background: linear-gradient(135deg, #718096 0%, #4a5568 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(113, 128, 150, 0.4);
}

.btn-action.secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(113, 128, 150, 0.5);
}

.btn-action.danger {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(245, 101, 101, 0.4);
}

.btn-action.danger:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(245, 101, 101, 0.5);
}

/* Students Table View */
.students-table-view {
    display: none;
    padding: 2rem;
}

.students-table-view.active {
    display: block;
}

.students-table-view .data-table {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(226, 232, 240, 0.6);
}

.students-table-view .data-table thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.students-table-view .data-table thead th {
    color: white;
    font-weight: 700;
    padding: 1.5rem 1rem;
    text-transform: uppercase;
    letter-spacing: 0.75px;
    font-size: 0.8125rem;
    border: none;
}

.students-table-view .data-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.students-table-view .data-table tbody tr:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    transform: scale(1.01);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.students-table-view .data-table tbody td {
    padding: 1.25rem 1rem;
    border: none;
    vertical-align: middle;
}

.students-table-view .student-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.students-table-view .student-avatar {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.students-table-view .student-details strong {
    font-size: 1rem;
    font-weight: 700;
    color: #1e293b;
    display: block;
    margin-bottom: 0.25rem;
}

.students-table-view .student-details small {
    font-size: 0.8125rem;
    color: #64748b;
    display: block;
    font-weight: 500;
}

.students-table-view .action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

/* Enhanced Status and Score Badges */
.status {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.status::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.8s ease;
}

.status:hover::before {
    left: 100%;
}

.status.approved {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
}

.status.pending {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(237, 137, 54, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.score-badge {
    padding: 0.5rem 1rem;
    border-radius: 16px;
    font-size: 0.8125rem;
    font-weight: 800;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.score-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.score-badge:hover::before {
    left: 100%;
}

.score-badge.good {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
}

.score-badge.average {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(237, 137, 54, 0.3);
}

.score-badge.poor {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(245, 101, 101, 0.3);
}

.no-data {
    color: #94a3b8;
    font-style: italic;
    font-weight: 500;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    padding: 0.5rem 1rem;
    border-radius: 16px;
    border: 1px solid #e2e8f0;
}

/* Enhanced Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 20px;
    margin: 2rem;
    border: 2px dashed #cbd5e0;
    position: relative;
    overflow: hidden;
}

.empty-state::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.empty-state i {
    font-size: 5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 1;
    animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.empty-state h3 {
    font-size: 1.75rem;
    font-weight: 800;
    color: #1e293b;
    margin-bottom: 0.75rem;
    position: relative;
    z-index: 1;
}

.empty-state p {
    margin-bottom: 2rem;
    font-size: 1.125rem;
    color: #64748b;
    font-weight: 500;
    position: relative;
    z-index: 1;
}

.empty-state-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    position: relative;
    z-index: 1;
}

.debug-info {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%) !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 16px !important;
    padding: 1.5rem !important;
    margin: 1.5rem 0 !important;
    text-align: left !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
}

.debug-info h4 {
    color: #1e293b !important;
    font-weight: 700 !important;
    margin-bottom: 1rem !important;
    font-size: 1.125rem !important;
}

.debug-info ul {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 0 1rem 0 !important;
}

.debug-info li {
    padding: 0.5rem 0 !important;
    border-bottom: 1px solid #f1f5f9 !important;
    color: #475569 !important;
    font-weight: 500 !important;
}

.debug-info li:last-child {
    border-bottom: none !important;
}

/* Enhanced Filter Controls */
.filter-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.filter-controls select {
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    color: #1e293b;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 140px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-controls select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.filter-controls select:hover {
    border-color: #cbd5e0;
    transform: translateY(-1px);
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #94a3b8;
    font-size: 0.875rem;
    z-index: 1;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    color: #1e293b;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.search-box input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.search-box input:hover {
    border-color: #cbd5e0;
    transform: translateY(-1px);
}

.search-box input::placeholder {
    color: #94a3b8;
    font-weight: 500;
}

/* Filters Section */
.filters-section {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.filters-form {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .filters-section {
        padding: 1rem;
    }

    .filters-form {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        min-width: auto;
    }

    .filter-controls {
        justify-content: center;
    }

    .filter-controls select {
        min-width: 120px;
        flex: 1;
    }
}

/* Department List */
.department-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.department-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.department-item:hover {
    border-color: #48bb78;
    background: #f0fff4;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Departments Grid */
.departments-container {
    margin-top: 2rem;
    width: 100%;
}

.departments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 2rem;
    margin-top: 1.5rem;
    padding: 0;
}

.department-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid #e2e8f0;
    overflow: hidden;
    position: relative;
    min-height: 280px;
}

.department-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #48bb78, #38a169, #2f855a);
}

.department-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 60px rgba(72, 187, 120, 0.2);
    border-color: #48bb78;
}

.department-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 2rem 1.5rem 2rem;
    background: transparent;
    border-bottom: none;
    position: relative;
}

.department-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #48bb78, #38a169);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.75rem;
    box-shadow: 0 8px 24px rgba(72, 187, 120, 0.3);
    position: relative;
}

.department-icon::after {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, #48bb78, #38a169);
    border-radius: 18px;
    z-index: -1;
    opacity: 0.2;
}

.department-actions {
    display: flex;
    gap: 0.75rem;
}

.department-actions .btn-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    border: none;
    background: #f1f5f9;
    color: #64748b;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.department-actions .btn-icon:hover {
    background: #e2e8f0;
    color: #1a202c;
    transform: scale(1.1);
}

.department-actions .btn-icon.delete:hover {
    background: #fee2e2;
    color: #dc2626;
}

.btn-icon.approve {
    background: #dcfce7;
    color: #16a34a;
}

.btn-icon.approve:hover {
    background: #16a34a;
    color: white;
    transform: scale(1.1);
}

.btn-icon.reject {
    background: #fef2f2;
    color: #dc2626;
}

.btn-icon.reject:hover {
    background: #dc2626;
    color: white;
    transform: scale(1.1);
}

.department-content {
    padding: 0 2rem 1.5rem 2rem;
}

.department-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.75rem;
    line-height: 1.3;
}

.department-code {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    display: inline-block;
    margin-bottom: 1rem;
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
}

.department-description {
    color: #64748b;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-weight: 400;
}

.department-stats {
    display: flex;
    gap: 0;
    margin: 0 2rem 1.5rem 2rem;
    padding: 1.5rem 0 0 0;
    border-top: 2px solid #f1f5f9;
}

.department-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
    padding: 0.75rem;
    border-radius: 12px;
    background: #f8fafc;
    margin-right: 0.75rem;
    transition: all 0.3s ease;
}

.department-stats .stat-item:last-child {
    margin-right: 0;
}

.department-stats .stat-item:hover {
    background: #e2e8f0;
    transform: translateY(-2px);
}

.department-stats .stat-item i {
    font-size: 1.25rem;
    color: #48bb78;
    width: 24px;
    text-align: center;
}

.department-stats .stat-item span {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1a202c;
    white-space: nowrap;
}

.department-footer {
    padding: 1rem 2rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    margin-top: auto;
}

.department-footer small {
    color: #64748b;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Questions Styles */
.questions-container {
    margin-top: 2rem;
}

.questions-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-top: 1rem;
}

.question-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid #e2e8f0;
    overflow: hidden;
    position: relative;
    margin-bottom: 1.5rem;
}

.question-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
}

.question-card:hover {
    transform: translateY(-4px) scale(1.01);
    box-shadow: 0 20px 60px rgba(102, 126, 234, 0.2);
    border-color: #667eea;
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid #e2e8f0;
}

.question-meta {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.subject-tag {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.difficulty-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.difficulty-badge.easy {
    background: #c6f6d5;
    color: #22543d;
}

.difficulty-badge.medium {
    background: #fed7cc;
    color: #c53030;
}

.difficulty-badge.hard {
    background: #fbb6ce;
    color: #97266d;
}

.question-actions {
    display: flex;
    gap: 0.5rem;
}

.question-content {
    padding: 1.5rem;
}

.question-text {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.question-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.option {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.option.correct {
    background: #c6f6d5;
    border-color: #48bb78;
}

.option-letter {
    width: 32px;
    height: 32px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.option.correct .option-letter {
    background: #48bb78;
}

.question-footer {
    padding: 1.5rem 2rem;
    background: #f8fafc;
    border-top: 2px solid #e2e8f0;
    color: #64748b;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Universal Card Enhancements */
.content-card, .stats-card, .activity-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid #e2e8f0;
    overflow: hidden;
    position: relative;
}

.content-card::before, .stats-card::before, .activity-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
}

.content-card:hover, .stats-card:hover, .activity-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

/* Table Enhancements */
.data-table {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 2px solid #e2e8f0;
}

.data-table thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.data-table thead th {
    color: white;
    font-weight: 600;
    padding: 1.5rem 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.data-table tbody tr {
    transition: all 0.3s ease;
}

.data-table tbody tr:hover {
    background: #f8fafc;
    transform: scale(1.01);
}

.data-table tbody td {
    padding: 1.25rem 1rem;
    border-bottom: 1px solid #e2e8f0;
}

/* Button Enhancements */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    padding: 0.875rem 1.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    border: none;
    border-radius: 12px;
    padding: 0.875rem 1.5rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(72, 187, 120, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(72, 187, 120, 0.4);
}

/* Navigation Enhancements */
.nav-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    margin: 1rem 0;
}

.nav-secondary {
    background: linear-gradient(135deg, #4f46e5, #7c3aed) !important;
    margin-top: 0.5rem;
}

.nav-secondary:hover {
    background: linear-gradient(135deg, #4338ca, #6d28d9) !important;
    transform: translateX(8px);
}

.nav-logout {
    background: linear-gradient(135deg, #dc2626, #b91c1c) !important;
    margin-top: 0.5rem;
}

.nav-logout:hover {
    background: linear-gradient(135deg, #b91c1c, #991b1b) !important;
    transform: translateX(8px);
}

/* Settings Page Styling */
.settings-container {
    background: white;
    border-radius: 24px;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 2px solid #e2e8f0;
}

.settings-tabs {
    display: flex;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 2px solid #e2e8f0;
    padding: 0;
    overflow-x: auto;
}

.tab-button {
    background: transparent;
    border: none;
    padding: 1.5rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    color: #64748b;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    white-space: nowrap;
    position: relative;
    min-width: 160px;
    justify-content: center;
}

.tab-button::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.tab-button:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.tab-button.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.tab-button.active::after {
    transform: scaleX(1);
    background: white;
}

.tab-button i {
    font-size: 1.1rem;
}

.tab-content {
    display: none;
    padding: 3rem;
    background: white;
}

.tab-content.active {
    display: block;
}

.settings-section {
    max-width: 800px;
}

.settings-section h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 3px solid #e2e8f0;
    position: relative;
}

.settings-section h3::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

/* Form Styling */
.form-group {
    margin-bottom: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.form-group label {
    display: block;
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 500;
    color: #1a202c;
    background: #f8fafc;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
    line-height: 1.6;
}

.form-group select {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 3rem;
}

/* Checkbox Styling */
.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
    padding: 1rem;
    border-radius: 12px;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
    font-weight: 500;
}

.checkbox-label:hover {
    background: #e2e8f0;
    border-color: #667eea;
}

.checkbox-label input[type="checkbox"] {
    width: 20px;
    height: 20px;
    margin: 0;
    cursor: pointer;
    accent-color: #667eea;
}

.checkmark {
    font-weight: 500;
    color: #374151;
}

/* Form Actions */
.form-actions {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid #e2e8f0;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.form-actions .btn {
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-radius: 12px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 180px;
    justify-content: center;
}

.form-actions .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.form-actions .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

.form-actions .btn i {
    font-size: 1.1rem;
}

/* Settings Cards */
.settings-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 2px solid #e2e8f0;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.settings-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    border-color: #667eea;
}

.settings-card h4 {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.settings-card h4 i {
    color: #667eea;
    font-size: 1.1rem;
}

.settings-card p {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

/* Info Boxes */
.info-box {
    background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
    border: 2px solid #81d4fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.info-box.warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-color: #ffc107;
}

.info-box.success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-color: #28a745;
}

.info-box.danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-color: #dc3545;
}

.info-box i {
    font-size: 1.25rem;
    margin-top: 0.25rem;
}

.info-box .info-content {
    flex: 1;
}

.info-box .info-content h5 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1a202c;
}

.info-box .info-content p {
    margin: 0;
    color: #374151;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .settings-tabs {
        flex-direction: column;
    }

    .tab-button {
        min-width: auto;
        justify-content: flex-start;
        padding: 1rem 1.5rem;
    }

    .tab-content {
        padding: 2rem 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        min-width: auto;
    }
}

.dept-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #48bb78, #38a169);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.dept-info {
    flex: 1;
}

.dept-info h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 0.25rem;
}

.dept-code {
    font-size: 0.875rem;
    color: #48bb78;
    font-weight: 600;
}

.dept-stats {
    display: flex;
    gap: 1rem;
}

.dept-stats .stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.25rem;
    font-weight: 700;
    color: #1a202c;
}

.stat-number.approved {
    color: #48bb78;
}

.stat-label {
    font-size: 0.75rem;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dept-arrow {
    color: #cbd5e0;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.department-item:hover .dept-arrow {
    color: #48bb78;
    transform: translateX(4px);
}

/* Quiz Activity Table */
.quiz-activity-table {
    overflow-x: auto;
}

.quiz-activity-table table {
    width: 100%;
    border-collapse: collapse;
}

.quiz-activity-table th,
.quiz-activity-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
}

.quiz-activity-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #4a5568;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.quiz-activity-table tr {
    transition: all 0.3s ease;
    cursor: pointer;
}

.quiz-activity-table tr:hover {
    background: #f8fafc;
}

.quiz-activity-table .student-info strong {
    color: #1a202c;
    font-weight: 600;
}

.quiz-activity-table .student-info small {
    display: block;
    color: #718096;
    font-size: 0.75rem;
}

.score-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.score-badge.good {
    background: #c6f6d5;
    color: #22543d;
}

.score-badge.average {
    background: #fef5e7;
    color: #c05621;
}

.score-badge.poor {
    background: #fed7d7;
    color: #c53030;
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    background: white;
    margin: 5% auto;
    border-radius: 16px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8fafc;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1a202c;
}

.close {
    font-size: 1.5rem;
    font-weight: bold;
    color: #718096;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #e53e3e;
}

.modal-body {
    padding: 2rem;
    max-height: 60vh;
    overflow-y: auto;
}

.loading {
    text-align: center;
    padding: 3rem;
    color: #718096;
}

.loading i {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #667eea;
}

.error {
    text-align: center;
    padding: 3rem;
    color: #e53e3e;
    background: #fed7d7;
    border-radius: 8px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .content-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .content-header {
        padding: 1.5rem;
    }

    .header-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .quick-actions {
        grid-template-columns: 1fr;
        padding: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        padding: 1rem 1.5rem;
    }

    .content-grid {
        padding: 0 1.5rem 1.5rem;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}

@media (max-width: 480px) {
    .action-card {
        flex-direction: column;
        text-align: center;
    }

    .action-arrow {
        display: none;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
    }

    .quiz-activity-table {
        font-size: 0.875rem;
    }

    .quiz-activity-table th,
    .quiz-activity-table td {
        padding: 0.75rem 0.5rem;
    }

    /* Students Management Mobile */
    .students-container {
        margin: 1rem;
        border-radius: 16px;
    }

    .students-header {
        padding: 1.5rem;
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .students-header h3 {
        font-size: 1.5rem;
    }

    .view-toggle {
        justify-content: center;
    }

    .students-grid-view,
    .students-table-view {
        padding: 1rem;
    }

    .students-cards {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .student-card {
        margin: 0;
    }

    .student-card-header {
        padding: 1.5rem 1.5rem 1rem 1.5rem;
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .student-card .student-avatar {
        width: 64px;
        height: 64px;
        font-size: 1.5rem;
    }

    .student-basic-info {
        text-align: center;
    }

    .student-basic-info h4 {
        font-size: 1.25rem;
    }

    .student-card-body {
        padding: 1rem 1.5rem 1.5rem 1.5rem;
    }

    .student-details-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .student-card-actions {
        padding: 1rem 1.5rem 1.5rem 1.5rem;
        flex-direction: column;
        gap: 0.75rem;
    }

    .btn-action {
        width: 100%;
        justify-content: center;
        padding: 1rem 1.25rem;
    }

    /* Table view mobile */
    .students-table-view .data-table {
        font-size: 0.875rem;
    }

    .students-table-view .data-table th,
    .students-table-view .data-table td {
        padding: 1rem 0.75rem;
    }

    .students-table-view .student-avatar {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .students-table-view .action-buttons {
        flex-direction: column;
        gap: 0.5rem;
    }

    .empty-state {
        margin: 1rem;
        padding: 3rem 1.5rem;
    }

    .empty-state i {
        font-size: 4rem;
    }

    .empty-state h3 {
        font-size: 1.5rem;
    }

    .empty-state p {
        font-size: 1rem;
    }

    .empty-state-actions {
        flex-direction: column;
        align-items: center;
    }
}

/* API Setup Page Styling */
.setup-guide {
    max-width: 1000px;
    margin: 0 auto;
}

.guide-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.guide-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.guide-header h2 {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    font-weight: 700;
}

.guide-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.setup-step {
    display: flex;
    align-items: flex-start;
    padding: 2rem;
    border-bottom: 1px solid #e2e8f0;
    gap: 1.5rem;
}

.setup-step:last-child {
    border-bottom: none;
}

.step-number {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    flex-shrink: 0;
    box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
}

.step-content {
    flex: 1;
}

.step-content h3 {
    margin: 0 0 0.5rem 0;
    color: #1e293b;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.step-content p {
    margin: 0 0 1rem 0;
    color: #64748b;
    line-height: 1.6;
}

.step-actions {
    margin-top: 1rem;
}

.pricing-info {
    margin: 1rem 0;
}

.pricing-card {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: 2px solid #28a745;
    border-radius: 12px;
    padding: 1.5rem;
}

.pricing-card h4 {
    margin: 0 0 1rem 0;
    color: #155724;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pricing-card ul {
    margin: 0;
    padding-left: 0;
    list-style: none;
}

.pricing-card li {
    color: #155724;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.warning-box {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 2px solid #ffc107;
    border-radius: 12px;
    padding: 1rem;
    margin: 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #856404;
}

.warning-box i {
    color: #f39c12;
    font-size: 1.2rem;
}

.quick-links {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin-bottom: 2rem;
}

.quick-links h3 {
    margin: 0 0 1.5rem 0;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.link-card {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    text-decoration: none;
    color: #475569;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-align: center;
}

.link-card:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.link-card i {
    font-size: 2rem;
}

.faq-section {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    padding: 2rem;
}

.faq-section h3 {
    margin: 0 0 1.5rem 0;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.faq-item {
    border-bottom: 1px solid #e2e8f0;
    padding: 1.5rem 0;
}

.faq-item:last-child {
    border-bottom: none;
}

.faq-item h4 {
    margin: 0 0 0.5rem 0;
    color: #1e293b;
    font-size: 1.1rem;
}

.faq-item p {
    margin: 0;
    color: #64748b;
    line-height: 1.6;
}
