<?php
/**
 * AI Question Generation Interface
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Require admin login
requireLogin('admin');

$pageTitle = 'AI Question Generation';

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'generate_questions':
            $department_id = isset($_POST['department_id']) ? (int)$_POST['department_id'] : 0;
            $questions_per_level = isset($_POST['questions_per_level']) ? (int)$_POST['questions_per_level'] : 5;

            // Hardcoded OpenAI API key
            $apiKey = "********************************************************************************************************************************************************************";

            // Generate questions using PHP
            $result = generateQuestionsWithAI($department_id, $questions_per_level, $apiKey);

            echo json_encode($result);
            exit;
            
        case 'check_api_status':
            $apiKey = "********************************************************************************************************************************************************************";
            $aiModel = 'gpt-3.5-turbo';

            echo json_encode([
                'api_configured' => true,
                'model' => $aiModel,
                'api_key_length' => strlen($apiKey)
            ]);
            exit;
    }
}

/**
 * Generate questions using OpenAI API directly in PHP
 */
function generateQuestionsWithAI($department_id, $questions_per_level, $apiKey) {
    try {
        // Get departments to process
        if ($department_id > 0) {
            $departments = fetchAll("SELECT id, name, code, description FROM departments WHERE id = ?", [$department_id]);
        } else {
            $departments = fetchAll("SELECT id, name, code, description FROM departments ORDER BY name");
        }

        if (empty($departments)) {
            return [
                'success' => false,
                'message' => 'No departments found to process.'
            ];
        }

        // Get all academic levels
        $academicLevels = fetchAll("SELECT id, level_name, level_code, description FROM academic_levels ORDER BY level_name");

        if (empty($academicLevels)) {
            return [
                'success' => false,
                'message' => 'No academic levels found. Please set up academic levels first.'
            ];
        }

        $totalGenerated = 0;
        $processedCombinations = [];

        foreach ($departments as $department) {
            foreach ($academicLevels as $level) {
                $questions = callOpenAIAPI($department, $level, $questions_per_level, $apiKey);

                if (!empty($questions)) {
                    $saved = saveQuestionsToDatabase($questions, $department['id'], $level['id']);
                    $totalGenerated += $saved;

                    $processedCombinations[] = [
                        'department' => $department['name'],
                        'level' => $level['level_name'],
                        'questions' => $saved
                    ];
                }

                // Small delay to avoid rate limiting
                usleep(500000); // 0.5 seconds
            }
        }

        return [
            'success' => true,
            'message' => "Successfully generated $totalGenerated questions across " . count($processedCombinations) . " department-level combinations!",
            'questions_generated' => $totalGenerated,
            'combinations_processed' => $processedCombinations
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Error generating questions: ' . $e->getMessage()
        ];
    }
}

/**
 * Call OpenAI API to generate questions
 */
function callOpenAIAPI($department, $academicLevel, $num_questions, $apiKey) {
    $prompt = generatePrompt($department, $academicLevel, $num_questions);

    $data = [
        'model' => 'gpt-3.5-turbo',
        'messages' => [
            [
                'role' => 'system',
                'content' => 'You are an expert educational content creator specializing in polytechnic-level curriculum. Generate high-quality, diverse multiple-choice questions that test both theoretical knowledge and practical application. Always return valid JSON format.'
            ],
            [
                'role' => 'user',
                'content' => $prompt
            ]
        ],
        'max_tokens' => 3000,
        'temperature' => 0.7
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/chat/completions');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $apiKey
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error || $httpCode !== 200) {
        error_log("OpenAI API Error: HTTP $httpCode, $error");
        return [];
    }

    $responseData = json_decode($response, true);

    if (!isset($responseData['choices'][0]['message']['content'])) {
        error_log("Invalid OpenAI response structure");
        return [];
    }

    $content = $responseData['choices'][0]['message']['content'];

    // Try to extract JSON from the response
    $jsonStart = strpos($content, '{');
    $jsonEnd = strrpos($content, '}');

    if ($jsonStart !== false && $jsonEnd !== false) {
        $jsonContent = substr($content, $jsonStart, $jsonEnd - $jsonStart + 1);
        $questionsData = json_decode($jsonContent, true);

        if (isset($questionsData['questions']) && is_array($questionsData['questions'])) {
            return $questionsData['questions'];
        }
    }

    error_log("Failed to parse JSON from OpenAI response: " . $content);
    return [];
}

/**
 * Generate prompt for AI
 */
function generatePrompt($department, $academicLevel, $num_questions) {
    return "Generate exactly $num_questions multiple-choice questions for the following educational context:

Department: {$department['name']} ({$department['code']})
Academic Level: {$academicLevel['level_name']} ({$academicLevel['level_code']})
Department Description: {$department['description']}
Level Description: {$academicLevel['description']}

Educational Context:
- Institution: Ogbonnaya Onu Polytechnic, Aba
- Program: {$department['name']} Department
- Level: {$academicLevel['level_name']} (Polytechnic Level)

Requirements:
- Each question must have exactly 4 options (A, B, C, D)
- Questions should cover the ENTIRE curriculum for {$department['name']} at {$academicLevel['level_name']} level
- Mix of theoretical, practical, and application-based questions
- Appropriate for polytechnic-level education in Nigeria
- Clear, unambiguous, and educationally valuable
- Diverse difficulty levels (easy, medium, hard)
- Cover multiple subject areas within the department
- Include real-world applications and industry-relevant scenarios

Return ONLY valid JSON in this exact format:
{
    \"questions\": [
        {
            \"question\": \"Question text here?\",
            \"options\": {
                \"A\": \"Option A text\",
                \"B\": \"Option B text\",
                \"C\": \"Option C text\",
                \"D\": \"Option D text\"
            },
            \"correct_answer\": \"A\",
            \"explanation\": \"Brief explanation of why this is correct\",
            \"difficulty\": \"easy\",
            \"topic\": \"Specific topic within the department curriculum\"
        }
    ]
}

Generate comprehensive questions covering the full {$department['name']} curriculum for {$academicLevel['level_name']} students at polytechnic level.";
}

/**
 * Save questions to database
 */
function saveQuestionsToDatabase($questions, $department_id, $academic_level_id) {
    $saved = 0;

    foreach ($questions as $q) {
        if (!isset($q['question']) || !isset($q['options']) || !isset($q['correct_answer'])) {
            continue;
        }

        $options = $q['options'];
        if (!isset($options['A']) || !isset($options['B']) || !isset($options['C']) || !isset($options['D'])) {
            continue;
        }

        $sql = "INSERT INTO questions (
            department_id, academic_level_id, question_text, option_a, option_b,
            option_c, option_d, correct_answer, explanation, difficulty_level,
            created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

        $params = [
            $department_id,
            $academic_level_id,
            $q['question'],
            $options['A'],
            $options['B'],
            $options['C'],
            $options['D'],
            $q['correct_answer'],
            $q['explanation'] ?? '',
            ucfirst($q['difficulty'] ?? 'Medium')
        ];

        if (executeQuery($sql, $params)) {
            $saved++;
        }
    }

    return $saved;
}

// Get departments for dropdown
$departments = [];
try {
    $departments = fetchAll("SELECT id, name, code FROM departments ORDER BY name");
    if (!$departments) {
        $departments = [];
    }
} catch (Exception $e) {
    $departments = [];
}

// Initialize question counts
$recentQuestionsCount = 0;
$totalQuestionsCount = 0;

// Check if questions table exists and get counts
try {
    $pdo = getConnection();
    $stmt = $pdo->query("SHOW TABLES LIKE 'questions'");
    $questionsTableExists = $stmt->rowCount() > 0;

    if ($questionsTableExists) {
        // Get recent questions count
        try {
            $recentResult = fetchOne("SELECT COUNT(*) as count FROM questions WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAYS)");
            $recentQuestionsCount = ($recentResult && isset($recentResult['count'])) ? (int)$recentResult['count'] : 0;
        } catch (Exception $e) {
            $recentQuestionsCount = 0;
        }

        // Get total questions count
        try {
            $totalResult = fetchOne("SELECT COUNT(*) as count FROM questions");
            $totalQuestionsCount = ($totalResult && isset($totalResult['count'])) ? (int)$totalResult['count'] : 0;
        } catch (Exception $e) {
            $totalQuestionsCount = 0;
        }
    }
} catch (Exception $e) {
    // If any database error occurs, use default values
    $recentQuestionsCount = 0;
    $totalQuestionsCount = 0;
}

// API is hardcoded and configured
$apiConfigured = true;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="../assets/images/logo.png" alt="Logo" onerror="this.style.display='none'">
                </div>
                <div class="admin-info">
                    <h3>Admin Panel</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>

            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="students.php" class="nav-item">
                    <i class="fas fa-users"></i>
                    <span>Students</span>
                </a>
                <a href="departments.php" class="nav-item">
                    <i class="fas fa-building"></i>
                    <span>Departments</span>
                </a>
                <a href="questions.php" class="nav-item">
                    <i class="fas fa-question-circle"></i>
                    <span>Questions</span>
                </a>
                <a href="generate-questions.php" class="nav-item active">
                    <i class="fas fa-magic"></i>
                    <span>Generate Questions</span>
                </a>
                <a href="rewards.php" class="nav-item">
                    <i class="fas fa-trophy"></i>
                    <span>Rewards</span>
                </a>
                <a href="analytics.php" class="nav-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>Analytics</span>
                </a>
                <a href="settings.php" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
                
                <div class="nav-divider"></div>
                
                <a href="../index.php" class="nav-item nav-secondary">
                    <i class="fas fa-home"></i>
                    <span>Back to Site</span>
                </a>
                <a href="../logout.php" class="nav-item nav-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="content-header">
                <h1><i class="fas fa-magic"></i> AI Question Generation</h1>
                <p>Generate department-specific questions using artificial intelligence</p>
            </div>

            <div class="content-body">
                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card primary">
                        <div class="stat-icon">
                            <i class="fas fa-question-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo number_format($totalQuestionsCount); ?></h3>
                            <p>Total Questions</p>
                            <div class="stat-change">
                                <i class="fas fa-database"></i>
                                <span>In question bank</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card success">
                        <div class="stat-icon">
                            <i class="fas fa-plus-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo number_format($recentQuestionsCount); ?></h3>
                            <p>Generated This Week</p>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                <span>Recent additions</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card info">
                        <div class="stat-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo count($departments); ?></h3>
                            <p>Active Departments</p>
                            <div class="stat-change">
                                <i class="fas fa-check-circle"></i>
                                <span>Ready for generation</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="api-status">Checking...</h3>
                            <p>API Status</p>
                            <div class="stat-change">
                                <i class="fas fa-wifi"></i>
                                <span id="api-model">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Question Generation Form -->
                <div class="content-card">
                    <div class="card-header">
                        <h3><i class="fas fa-magic"></i> Generate New Questions</h3>
                    </div>
                    <div class="card-content">
                        <form id="generateForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="department_id">
                                        <i class="fas fa-building"></i>
                                        Target Department
                                    </label>
                                    <select id="department_id" name="department_id">
                                        <option value="0">All Departments</option>
                                        <?php foreach ($departments as $dept): ?>
                                            <option value="<?php echo $dept['id']; ?>">
                                                <?php echo htmlspecialchars($dept['name']); ?> (<?php echo htmlspecialchars($dept['code']); ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="questions_per_level">
                                        <i class="fas fa-hashtag"></i>
                                        Questions per Academic Level
                                    </label>
                                    <select id="questions_per_level" name="questions_per_level">
                                        <option value="5">5 Questions</option>
                                        <option value="10" selected>10 Questions</option>
                                        <option value="15">15 Questions</option>
                                        <option value="20">20 Questions</option>
                                        <option value="25">25 Questions</option>
                                    </select>
                                </div>
                            </div>

                            <div class="info-box">
                                <i class="fas fa-info-circle"></i>
                                <div class="info-content">
                                    <h5>Generation Process</h5>
                                    <p>The AI will generate comprehensive questions for each department and academic level combination. Questions will cover the full curriculum for each level, eliminating the need for subject-specific categorization.</p>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary" id="generateBtn" <?php echo !$apiConfigured ? 'disabled' : ''; ?>>
                                    <i class="fas fa-magic"></i>
                                    <span>Generate Questions</span>
                                </button>
                            </div>
                        </form>

                        <!-- Progress and Results -->
                        <div id="generationProgress" style="display: none; margin-top: 2rem;">
                            <div class="info-box">
                                <i class="fas fa-spinner fa-spin"></i>
                                <div class="info-content">
                                    <h5>Generating Questions...</h5>
                                    <p>Please wait while the AI generates your questions. This may take a few minutes.</p>
                                </div>
                            </div>
                        </div>

                        <div id="generationResults" style="display: none; margin-top: 2rem;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Check API status on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkApiStatus();
        });

        function checkApiStatus() {
            fetch('generate-questions.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=check_api_status'
            })
            .then(response => response.json())
            .then(data => {
                const statusElement = document.getElementById('api-status');
                const modelElement = document.getElementById('api-model');
                
                if (data.api_configured) {
                    statusElement.textContent = 'Ready';
                    statusElement.parentElement.parentElement.className = 'stat-card success';
                    modelElement.textContent = data.model;
                } else {
                    statusElement.textContent = 'Not Configured';
                    statusElement.parentElement.parentElement.className = 'stat-card danger';
                    modelElement.textContent = 'API key required';
                }
            })
            .catch(error => {
                console.error('Error checking API status:', error);
                document.getElementById('api-status').textContent = 'Error';
                document.getElementById('api-model').textContent = 'Check failed';
            });
        }

        // Handle form submission
        document.getElementById('generateForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            formData.append('action', 'generate_questions');
            
            const generateBtn = document.getElementById('generateBtn');
            const progressDiv = document.getElementById('generationProgress');
            const resultsDiv = document.getElementById('generationResults');
            
            // Show progress
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
            progressDiv.style.display = 'block';
            resultsDiv.style.display = 'none';
            
            fetch('generate-questions.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                progressDiv.style.display = 'none';
                resultsDiv.style.display = 'block';
                
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <div class="info-box success">
                            <i class="fas fa-check-circle"></i>
                            <div class="info-content">
                                <h5>Success!</h5>
                                <p>${data.message}</p>
                                ${data.questions_generated ? `<p><strong>Questions Generated:</strong> ${data.questions_generated}</p>` : ''}
                            </div>
                        </div>
                    `;
                    
                    // Refresh page after 3 seconds to update stats
                    setTimeout(() => {
                        window.location.reload();
                    }, 3000);
                } else {
                    resultsDiv.innerHTML = `
                        <div class="info-box danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            <div class="info-content">
                                <h5>Generation Failed</h5>
                                <p>${data.message}</p>
                                ${data.redirect ? `<div style="margin-top: 1rem;"><a href="${data.redirect}" class="btn btn-primary">Configure API</a></div>` : ''}
                            </div>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                progressDiv.style.display = 'none';
                resultsDiv.style.display = 'block';
                resultsDiv.innerHTML = `
                    <div class="info-box danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <div class="info-content">
                            <h5>Error</h5>
                            <p>An unexpected error occurred. Please try again.</p>
                        </div>
                    </div>
                `;
            })
            .finally(() => {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-magic"></i> Generate Questions';
            });
        });
    </script>
</body>
</html>
