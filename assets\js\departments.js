/**
 * Departments Management JavaScript
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeDepartmentsPage();
});

function initializeDepartmentsPage() {
    // Initialize form validation
    initializeFormValidation();
    
    // Initialize keyboard shortcuts
    initializeKeyboardShortcuts();
    
    // Initialize tooltips
    initializeTooltips();
}

function initializeFormValidation() {
    const form = document.getElementById('departmentForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validateDepartmentForm()) {
                e.preventDefault();
            }
        });
        
        // Real-time validation
        const nameInput = document.getElementById('name');
        const codeInput = document.getElementById('code');
        
        if (nameInput) {
            nameInput.addEventListener('input', validateName);
        }
        
        if (codeInput) {
            codeInput.addEventListener('input', validateCode);
        }
    }
}

function validateDepartmentForm() {
    let isValid = true;
    
    // Validate name
    if (!validateName()) {
        isValid = false;
    }
    
    // Validate code
    if (!validateCode()) {
        isValid = false;
    }
    
    return isValid;
}

function validateName() {
    const nameInput = document.getElementById('name');
    const name = nameInput.value.trim();
    
    clearFieldError(nameInput);
    
    if (name.length < 2) {
        showFieldError(nameInput, 'Department name must be at least 2 characters');
        return false;
    }
    
    if (name.length > 100) {
        showFieldError(nameInput, 'Department name must be less than 100 characters');
        return false;
    }
    
    return true;
}

function validateCode() {
    const codeInput = document.getElementById('code');
    const code = codeInput.value.trim().toUpperCase();
    
    clearFieldError(codeInput);
    
    if (code.length < 2) {
        showFieldError(codeInput, 'Department code must be at least 2 characters');
        return false;
    }
    
    if (code.length > 10) {
        showFieldError(codeInput, 'Department code must be less than 10 characters');
        return false;
    }
    
    if (!/^[A-Z0-9]+$/.test(code)) {
        showFieldError(codeInput, 'Department code can only contain letters and numbers');
        return false;
    }
    
    // Auto-format code to uppercase
    codeInput.value = code;
    
    return true;
}

function showFieldError(input, message) {
    const formGroup = input.closest('.form-group');
    let errorElement = formGroup.querySelector('.field-error');
    
    if (!errorElement) {
        errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        formGroup.appendChild(errorElement);
    }
    
    errorElement.textContent = message;
    input.classList.add('error');
}

function clearFieldError(input) {
    const formGroup = input.closest('.form-group');
    const errorElement = formGroup.querySelector('.field-error');
    
    if (errorElement) {
        errorElement.remove();
    }
    
    input.classList.remove('error');
}

function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + N for new department
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            openAddDepartmentModal();
        }
        
        // Escape to close modal
        if (e.key === 'Escape') {
            closeDepartmentModal();
        }
    });
}

function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[title]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(event) {
    const element = event.target.closest('[title]');
    const title = element.getAttribute('title');
    
    if (title) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = title;
        tooltip.style.position = 'absolute';
        tooltip.style.zIndex = '1000';
        tooltip.style.backgroundColor = '#333';
        tooltip.style.color = 'white';
        tooltip.style.padding = '5px 10px';
        tooltip.style.borderRadius = '4px';
        tooltip.style.fontSize = '12px';
        tooltip.style.whiteSpace = 'nowrap';
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
        
        element._tooltip = tooltip;
    }
}

function hideTooltip(event) {
    const element = event.target.closest('[title]');
    if (element._tooltip) {
        document.body.removeChild(element._tooltip);
        element._tooltip = null;
    }
}

function openAddDepartmentModal() {
    const modal = document.getElementById('departmentModal');
    const form = document.getElementById('departmentForm');
    const modalTitle = document.getElementById('modalTitle');
    const submitText = document.getElementById('submitText');
    const formAction = document.getElementById('formAction');
    
    // Reset form
    form.reset();
    clearAllFieldErrors();
    
    // Set modal for adding
    modalTitle.textContent = 'Add New Department';
    submitText.textContent = 'Add Department';
    formAction.value = 'add_department';
    
    // Show modal
    modal.style.display = 'block';
    
    // Focus on name input
    setTimeout(() => {
        document.getElementById('name').focus();
    }, 100);
}

function editDepartment(departmentId) {
    // Get department data from the card
    const departmentCard = document.querySelector(`[data-department-id="${departmentId}"]`);
    if (!departmentCard) {
        // Fallback: fetch department data via AJAX
        fetchDepartmentData(departmentId);
        return;
    }
    
    const name = departmentCard.querySelector('h3').textContent;
    const code = departmentCard.querySelector('.department-code').textContent;
    const description = departmentCard.querySelector('.department-description')?.textContent || '';
    
    openEditDepartmentModal(departmentId, name, code, description);
}

function fetchDepartmentData(departmentId) {
    fetch(`ajax/get-department.php?id=${departmentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                openEditDepartmentModal(
                    departmentId,
                    data.department.name,
                    data.department.code,
                    data.department.description
                );
            } else {
                showErrorMessage('Error loading department data');
            }
        })
        .catch(error => {
            showErrorMessage('Error loading department data: ' + error.message);
        });
}

function openEditDepartmentModal(departmentId, name, code, description) {
    const modal = document.getElementById('departmentModal');
    const form = document.getElementById('departmentForm');
    const modalTitle = document.getElementById('modalTitle');
    const submitText = document.getElementById('submitText');
    const formAction = document.getElementById('formAction');
    const departmentIdInput = document.getElementById('departmentId');
    
    // Clear any existing errors
    clearAllFieldErrors();
    
    // Set form values
    document.getElementById('name').value = name;
    document.getElementById('code').value = code;
    document.getElementById('description').value = description;
    departmentIdInput.value = departmentId;
    
    // Set modal for editing
    modalTitle.textContent = 'Edit Department';
    submitText.textContent = 'Update Department';
    formAction.value = 'edit_department';
    
    // Show modal
    modal.style.display = 'block';
    
    // Focus on name input
    setTimeout(() => {
        document.getElementById('name').focus();
    }, 100);
}

function closeDepartmentModal() {
    const modal = document.getElementById('departmentModal');
    modal.style.display = 'none';
    
    // Clear form
    document.getElementById('departmentForm').reset();
    clearAllFieldErrors();
}

function clearAllFieldErrors() {
    const errorElements = document.querySelectorAll('.field-error');
    errorElements.forEach(element => element.remove());
    
    const errorInputs = document.querySelectorAll('.error');
    errorInputs.forEach(input => input.classList.remove('error'));
}

function deleteDepartment(departmentId, departmentName) {
    if (confirm(`Are you sure you want to delete "${departmentName}"?\n\nThis action cannot be undone and will fail if the department has students or subjects.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_department">
            <input type="hidden" name="id" value="${departmentId}">
        `;
        
        document.body.appendChild(form);
        form.submit();
    }
}

function showErrorMessage(message) {
    const alert = document.createElement('div');
    alert.className = 'alert alert-error';
    alert.innerHTML = `
        <i class="fas fa-exclamation-circle"></i>
        ${message}
    `;
    
    const mainContent = document.querySelector('.main-content');
    const header = mainContent.querySelector('.content-header');
    
    mainContent.insertBefore(alert, header.nextSibling);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.parentNode.removeChild(alert);
        }
    }, 5000);
}

function showSuccessMessage(message) {
    const alert = document.createElement('div');
    alert.className = 'alert alert-success';
    alert.innerHTML = `
        <i class="fas fa-check-circle"></i>
        ${message}
    `;
    
    const mainContent = document.querySelector('.main-content');
    const header = mainContent.querySelector('.content-header');
    
    mainContent.insertBefore(alert, header.nextSibling);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.parentNode.removeChild(alert);
        }
    }, 5000);
}

// Close modal when clicking outside
document.addEventListener('click', function(e) {
    const modal = document.getElementById('departmentModal');
    if (e.target === modal) {
        closeDepartmentModal();
    }
});

// Export functions for global access
window.openAddDepartmentModal = openAddDepartmentModal;
window.editDepartment = editDepartment;
window.closeDepartmentModal = closeDepartmentModal;
window.deleteDepartment = deleteDepartment;
