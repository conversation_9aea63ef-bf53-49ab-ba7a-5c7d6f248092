/**
 * Subjects Management JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeSubjectsPage();
});

function initializeSubjectsPage() {
    // Initialize form validation
    const form = document.getElementById('subjectForm');
    if (form) {
        form.addEventListener('submit', validateSubjectForm);
    }
    
    // Initialize search and filter functionality
    initializeSearch();
    
    // Auto-hide alerts after 5 seconds
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            alert.style.opacity = '0';
            setTimeout(() => alert.remove(), 300);
        });
    }, 5000);
}

function openAddSubjectModal() {
    const modal = document.getElementById('subjectModal');
    const form = document.getElementById('subjectForm');
    const title = document.getElementById('modalTitle');
    const action = document.getElementById('formAction');
    const submitText = document.getElementById('submitText');
    
    // Reset form
    form.reset();
    document.getElementById('subjectId').value = '';
    
    // Set modal for adding
    title.textContent = 'Add New Subject';
    action.value = 'add_subject';
    submitText.textContent = 'Add Subject';
    
    // Show modal
    modal.style.display = 'block';
    
    // Focus on first input
    document.getElementById('name').focus();
}

function editSubject(subjectId) {
    // Get subject data from the card
    const subjectCard = document.querySelector(`[data-subject-id="${subjectId}"]`) || 
                       Array.from(document.querySelectorAll('.subject-card')).find(card => {
                           return card.querySelector('.btn-icon[onclick*="' + subjectId + '"]');
                       });
    
    if (!subjectCard) {
        showNotification('Subject not found', 'error');
        return;
    }
    
    // Extract data from the card
    const name = subjectCard.querySelector('h3').textContent.trim();
    const code = subjectCard.querySelector('.subject-code').textContent.trim();
    const description = subjectCard.querySelector('.subject-description')?.textContent.trim() || '';
    
    // Open modal
    const modal = document.getElementById('subjectModal');
    const form = document.getElementById('subjectForm');
    const title = document.getElementById('modalTitle');
    const action = document.getElementById('formAction');
    const submitText = document.getElementById('submitText');
    
    // Set modal for editing
    title.textContent = 'Edit Subject';
    action.value = 'edit_subject';
    submitText.textContent = 'Update Subject';
    
    // Fill form with current data
    document.getElementById('subjectId').value = subjectId;
    document.getElementById('name').value = name;
    document.getElementById('code').value = code;
    document.getElementById('description').value = description;
    
    // Show modal
    modal.style.display = 'block';
    
    // Focus on name input
    document.getElementById('name').focus();
}

function deleteSubject(subjectId, subjectName) {
    if (confirm(`Are you sure you want to delete the subject "${subjectName}"?\n\nThis action cannot be undone.`)) {
        // Create and submit delete form
        const form = document.createElement('form');
        form.method = 'POST';
        form.style.display = 'none';
        
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'delete_subject';
        
        const idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'id';
        idInput.value = subjectId;
        
        form.appendChild(actionInput);
        form.appendChild(idInput);
        document.body.appendChild(form);
        
        form.submit();
    }
}

function closeSubjectModal() {
    const modal = document.getElementById('subjectModal');
    modal.style.display = 'none';
    
    // Reset form
    document.getElementById('subjectForm').reset();
}

function validateSubjectForm(e) {
    const form = e.target;
    const name = form.name.value.trim();
    const code = form.code.value.trim();
    const departmentId = form.department_id.value;
    const levelId = form.academic_level_id.value;
    
    // Basic validation
    if (!name) {
        showNotification('Subject name is required', 'error');
        form.name.focus();
        e.preventDefault();
        return false;
    }
    
    if (!code) {
        showNotification('Subject code is required', 'error');
        form.code.focus();
        e.preventDefault();
        return false;
    }
    
    if (!departmentId) {
        showNotification('Please select a department', 'error');
        form.department_id.focus();
        e.preventDefault();
        return false;
    }
    
    if (!levelId) {
        showNotification('Please select an academic level', 'error');
        form.academic_level_id.focus();
        e.preventDefault();
        return false;
    }
    
    // Validate subject code format
    const codePattern = /^[A-Z]{2,4}\d{3}$/;
    if (!codePattern.test(code.toUpperCase())) {
        showNotification('Subject code should be in format like MTH101, ENG201, etc.', 'error');
        form.code.focus();
        e.preventDefault();
        return false;
    }
    
    // Convert code to uppercase
    form.code.value = code.toUpperCase();
    
    return true;
}

function initializeSearch() {
    // Create search and filter controls
    const header = document.querySelector('.content-header');
    const searchContainer = document.createElement('div');
    searchContainer.className = 'search-filters';
    searchContainer.innerHTML = `
        <div class="search-box">
            <i class="fas fa-search"></i>
            <input type="text" id="subjectSearch" placeholder="Search subjects...">
        </div>
        <div class="filter-controls">
            <select id="departmentFilter">
                <option value="">All Departments</option>
            </select>
            <select id="levelFilter">
                <option value="">All Levels</option>
            </select>
        </div>
    `;
    
    // Insert after header
    header.parentNode.insertBefore(searchContainer, header.nextSibling);
    
    // Populate filter options
    populateFilterOptions();
    
    // Add event listeners
    document.getElementById('subjectSearch').addEventListener('input', filterSubjects);
    document.getElementById('departmentFilter').addEventListener('change', filterSubjects);
    document.getElementById('levelFilter').addEventListener('change', filterSubjects);
}

function populateFilterOptions() {
    const departmentFilter = document.getElementById('departmentFilter');
    const levelFilter = document.getElementById('levelFilter');
    const subjectCards = document.querySelectorAll('.subject-card');
    
    const departments = new Set();
    const levels = new Set();
    
    subjectCards.forEach(card => {
        const dept = card.querySelector('.subject-dept').textContent.trim();
        const level = card.querySelector('.subject-level').textContent.trim();
        departments.add(dept);
        levels.add(level);
    });
    
    // Populate department filter
    departments.forEach(dept => {
        const option = document.createElement('option');
        option.value = dept;
        option.textContent = dept;
        departmentFilter.appendChild(option);
    });
    
    // Populate level filter
    levels.forEach(level => {
        const option = document.createElement('option');
        option.value = level;
        option.textContent = level;
        levelFilter.appendChild(option);
    });
}

function filterSubjects() {
    const searchTerm = document.getElementById('subjectSearch').value.toLowerCase();
    const departmentFilter = document.getElementById('departmentFilter').value;
    const levelFilter = document.getElementById('levelFilter').value;
    const subjectCards = document.querySelectorAll('.subject-card');
    
    let visibleCount = 0;
    
    subjectCards.forEach(card => {
        const name = card.querySelector('h3').textContent.toLowerCase();
        const code = card.querySelector('.subject-code').textContent.toLowerCase();
        const dept = card.querySelector('.subject-dept').textContent;
        const level = card.querySelector('.subject-level').textContent;
        const description = card.querySelector('.subject-description')?.textContent.toLowerCase() || '';
        
        const matchesSearch = !searchTerm || 
                            name.includes(searchTerm) || 
                            code.includes(searchTerm) || 
                            description.includes(searchTerm);
        
        const matchesDepartment = !departmentFilter || dept === departmentFilter;
        const matchesLevel = !levelFilter || level === levelFilter;
        
        if (matchesSearch && matchesDepartment && matchesLevel) {
            card.style.display = 'block';
            visibleCount++;
        } else {
            card.style.display = 'none';
        }
    });
    
    // Show/hide empty state
    const emptyState = document.querySelector('.empty-state');
    const subjectsGrid = document.querySelector('.subjects-grid');
    
    if (visibleCount === 0 && subjectCards.length > 0) {
        if (!document.querySelector('.no-results')) {
            const noResults = document.createElement('div');
            noResults.className = 'no-results empty-state';
            noResults.innerHTML = `
                <i class="fas fa-search"></i>
                <h3>No Subjects Found</h3>
                <p>Try adjusting your search criteria</p>
                <button class="btn btn-secondary" onclick="clearFilters()">Clear Filters</button>
            `;
            subjectsGrid.parentNode.appendChild(noResults);
        }
        document.querySelector('.no-results').style.display = 'block';
        if (subjectsGrid) subjectsGrid.style.display = 'none';
    } else {
        const noResults = document.querySelector('.no-results');
        if (noResults) noResults.style.display = 'none';
        if (subjectsGrid) subjectsGrid.style.display = 'grid';
    }
}

function clearFilters() {
    document.getElementById('subjectSearch').value = '';
    document.getElementById('departmentFilter').value = '';
    document.getElementById('levelFilter').value = '';
    filterSubjects();
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('subjectModal');
    if (event.target === modal) {
        closeSubjectModal();
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Escape to close modal
    if (e.key === 'Escape') {
        closeSubjectModal();
    }
    
    // Ctrl+N to add new subject
    if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault();
        openAddSubjectModal();
    }
    
    // Ctrl+F to focus search
    if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault();
        const searchInput = document.getElementById('subjectSearch');
        if (searchInput) {
            searchInput.focus();
        }
    }
});

// Add CSS for search and filters
const style = document.createElement('style');
style.textContent = `
    .search-filters {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem 2.5rem;
        background: white;
        border-bottom: 1px solid #e2e8f0;
        gap: 2rem;
    }
    
    .search-box {
        position: relative;
        flex: 1;
        max-width: 400px;
    }
    
    .search-box i {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #718096;
    }
    
    .search-box input {
        width: 100%;
        padding: 0.75rem 1rem 0.75rem 2.5rem;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        font-size: 0.875rem;
    }
    
    .search-box input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .filter-controls {
        display: flex;
        gap: 1rem;
    }
    
    .filter-controls select {
        padding: 0.75rem 1rem;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        font-size: 0.875rem;
        min-width: 150px;
    }
    
    .filter-controls select:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .subjects-container {
        padding: 2rem 2.5rem;
    }
    
    .subjects-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;
    }
    
    .subject-card {
        background: white;
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
    }
    
    .subject-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    .subject-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .subject-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #ed8936, #dd6b20);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
    }
    
    .subject-actions {
        display: flex;
        gap: 0.5rem;
    }
    
    .subject-content h3 {
        font-size: 1.25rem;
        font-weight: 700;
        color: #1a202c;
        margin-bottom: 0.5rem;
    }
    
    .subject-code {
        font-size: 0.875rem;
        color: #ed8936;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .subject-dept, .subject-level {
        font-size: 0.875rem;
        color: #718096;
        margin-bottom: 0.25rem;
    }
    
    .subject-description {
        font-size: 0.875rem;
        color: #4a5568;
        margin-top: 0.75rem;
        line-height: 1.5;
    }
    
    .subject-footer {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e2e8f0;
    }
    
    .subject-stats .stat-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: #718096;
    }
    
    .subject-stats i {
        color: #667eea;
    }
    
    .btn-icon.delete:hover {
        background: #e53e3e;
        color: white;
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    
    .alert {
        padding: 1rem 2.5rem;
        margin: 0;
        border: none;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-weight: 500;
        transition: opacity 0.3s ease;
    }
    
    .alert-success {
        background: #c6f6d5;
        color: #22543d;
    }
    
    .alert-error {
        background: #fed7d7;
        color: #c53030;
    }
    
    @media (max-width: 768px) {
        .search-filters {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }
        
        .filter-controls {
            justify-content: space-between;
        }
        
        .subjects-grid {
            grid-template-columns: 1fr;
        }
        
        .form-row {
            grid-template-columns: 1fr;
        }
    }
`;
document.head.appendChild(style);
