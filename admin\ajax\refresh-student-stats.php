<?php
/**
 * Refresh Student Statistics AJAX Endpoint
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../../config/database.php';

// Set JSON header
header('Content-Type: application/json');

try {
    // Require admin login
    requireLogin('admin');
    
    // Get student statistics
    $totalStudents = fetchOne("SELECT COUNT(*) as count FROM students")['count'];
    $approvedStudents = fetchOne("SELECT COUNT(*) as count FROM students WHERE is_approved = 1")['count'];
    $pendingStudents = fetchOne("SELECT COUNT(*) as count FROM students WHERE is_approved = 0")['count'];
    $newStudentsToday = fetchOne("SELECT COUNT(*) as count FROM students WHERE DATE(created_at) = CURDATE()")['count'];
    $newStudentsThisWeek = fetchOne("SELECT COUNT(*) as count FROM students WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")['count'];
    
    // Get department statistics
    $totalDepartments = fetchOne("SELECT COUNT(*) as count FROM departments")['count'];
    
    // Get department statistics
    $totalDepartments = fetchOne("SELECT COUNT(*) as count FROM departments")['count'];
    
    // Get quiz statistics
    $totalQuizzes = fetchOne("SELECT COUNT(*) as count FROM quiz_sessions")['count'];
    $quizzesToday = fetchOne("SELECT COUNT(*) as count FROM quiz_sessions WHERE DATE(created_at) = CURDATE()")['count'];
    
    // Get question statistics
    $totalQuestions = fetchOne("SELECT COUNT(*) as count FROM questions")['count'];
    
    // Get recent activity
    $recentStudents = fetchAll("
        SELECT s.*, d.name as department_name, al.level_name
        FROM students s
        JOIN departments d ON s.department_id = d.id
        JOIN academic_levels al ON s.academic_level_id = al.id
        WHERE s.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY s.created_at DESC
        LIMIT 5
    ");
    
    $recentQuizzes = fetchAll("
        SELECT qs.*, s.first_name, s.last_name, s.student_id,
               d.name as department_name, al.level_name
        FROM quiz_sessions qs
        JOIN students s ON qs.student_id = s.id
        JOIN departments d ON qs.department_id = d.id
        JOIN academic_levels al ON qs.academic_level_id = al.id
        WHERE qs.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY qs.created_at DESC
        LIMIT 5
    ");
    
    // Calculate performance metrics
    $avgScore = fetchOne("SELECT AVG(score/total_questions*100) as avg_score FROM quiz_sessions WHERE score IS NOT NULL")['avg_score'];
    $completionRate = fetchOne("
        SELECT 
            (COUNT(CASE WHEN status = 'completed' THEN 1 END) / COUNT(*) * 100) as completion_rate
        FROM quiz_sessions
    ")['completion_rate'];
    
    // Get top performing students
    $topStudents = fetchAll("
        SELECT s.first_name, s.last_name, s.student_id,
               AVG(qs.score/qs.total_questions*100) as avg_score,
               COUNT(qs.id) as quiz_count
        FROM students s
        JOIN quiz_sessions qs ON s.id = qs.student_id
        WHERE qs.status = 'completed' AND qs.score IS NOT NULL
        GROUP BY s.id
        HAVING quiz_count >= 3
        ORDER BY avg_score DESC
        LIMIT 5
    ");
    
    // Get department performance
    $departmentStats = fetchAll("
        SELECT d.name as department_name,
               COUNT(DISTINCT s.id) as student_count,
               COUNT(DISTINCT qs.id) as quiz_count,
               AVG(qs.score/qs.total_questions*100) as avg_score
        FROM departments d
        LEFT JOIN students s ON d.id = s.department_id AND s.is_approved = 1
        LEFT JOIN quiz_sessions qs ON s.id = qs.student_id AND qs.status = 'completed'
        GROUP BY d.id, d.name
        ORDER BY d.name
    ");
    
    // Response data
    $response = [
        'success' => true,
        'stats' => [
            'total' => (int)$totalStudents,
            'approved' => (int)$approvedStudents,
            'pending' => (int)$pendingStudents,
            'new_today' => (int)$newStudentsToday,
            'new_this_week' => (int)$newStudentsThisWeek,
            'departments' => (int)$totalDepartments,
            'subjects' => (int)$totalSubjects,
            'quizzes' => (int)$totalQuizzes,
            'quizzes_today' => (int)$quizzesToday,
            'questions' => (int)$totalQuestions,
            'avg_score' => round($avgScore ?? 0, 1),
            'completion_rate' => round($completionRate ?? 0, 1)
        ],
        'recent_activity' => [
            'students' => $recentStudents,
            'quizzes' => $recentQuizzes
        ],
        'performance' => [
            'top_students' => $topStudents,
            'department_stats' => $departmentStats
        ],
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'stats' => [
            'total' => 0,
            'approved' => 0,
            'pending' => 0,
            'new_today' => 0,
            'new_this_week' => 0,
            'departments' => 0,
            'subjects' => 0,
            'quizzes' => 0,
            'quizzes_today' => 0,
            'questions' => 0,
            'avg_score' => 0,
            'completion_rate' => 0
        ]
    ]);
}
?>
