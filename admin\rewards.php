<?php
/**
 * Rewards Management for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Require admin login
requireLogin('admin');

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'award_points':
                    $student_id = (int)$_POST['student_id'];
                    $points = (int)$_POST['points'];
                    $reason = trim($_POST['reason']);
                    
                    if (!$student_id || !$points || !$reason) {
                        throw new Exception('Student, points, and reason are required');
                    }
                    
                    // Add points to student
                    execute("
                        UPDATE students 
                        SET total_points = total_points + :points, updated_at = NOW()
                        WHERE id = :student_id
                    ", ['points' => $points, 'student_id' => $student_id]);
                    
                    // Record the reward
                    execute("
                        INSERT INTO student_rewards (student_id, points_awarded, reason, awarded_by, created_at)
                        VALUES (:student_id, :points, :reason, :admin_id, NOW())
                    ", [
                        'student_id' => $student_id,
                        'points' => $points,
                        'reason' => $reason,
                        'admin_id' => $_SESSION['admin_id']
                    ]);
                    
                    $success = "Successfully awarded {$points} points!";
                    break;
                    
                case 'create_badge':
                    $name = trim($_POST['name']);
                    $description = trim($_POST['description']);
                    $icon = trim($_POST['icon']);
                    $points_required = (int)$_POST['points_required'];
                    $criteria = trim($_POST['criteria']);
                    
                    if (!$name || !$description || !$points_required) {
                        throw new Exception('Name, description, and points required are mandatory');
                    }
                    
                    execute("
                        INSERT INTO badges (name, description, icon, points_required, criteria, created_at)
                        VALUES (:name, :description, :icon, :points_required, :criteria, NOW())
                    ", [
                        'name' => $name,
                        'description' => $description,
                        'icon' => $icon,
                        'points_required' => $points_required,
                        'criteria' => $criteria
                    ]);
                    
                    $success = "Badge created successfully!";
                    break;
                    
                case 'award_badge':
                    $student_id = (int)$_POST['student_id'];
                    $badge_id = (int)$_POST['badge_id'];
                    
                    // Check if student already has this badge
                    $existing = fetchOne("
                        SELECT id FROM student_badges 
                        WHERE student_id = :student_id AND badge_id = :badge_id
                    ", ['student_id' => $student_id, 'badge_id' => $badge_id]);
                    
                    if ($existing) {
                        throw new Exception('Student already has this badge');
                    }
                    
                    execute("
                        INSERT INTO student_badges (student_id, badge_id, awarded_by, created_at)
                        VALUES (:student_id, :badge_id, :admin_id, NOW())
                    ", [
                        'student_id' => $student_id,
                        'badge_id' => $badge_id,
                        'admin_id' => $_SESSION['admin_id']
                    ]);
                    
                    $success = "Badge awarded successfully!";
                    break;
            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get students for rewards
$students = fetchAll("
    SELECT s.*, d.name as department_name, al.level_name,
           (SELECT COUNT(*) FROM quiz_sessions WHERE student_id = s.id AND status = 'completed') as completed_quizzes,
           (SELECT AVG(score/total_questions*100) FROM quiz_sessions WHERE student_id = s.id AND status = 'completed') as avg_score
    FROM students s
    JOIN departments d ON s.department_id = d.id
    JOIN academic_levels al ON s.academic_level_id = al.id
    WHERE s.is_approved = 1
    ORDER BY s.total_points DESC, s.first_name
");

// Get badges
$badges = fetchAll("SELECT * FROM badges ORDER BY points_required");

// Ensure badges is always an array
if (!is_array($badges)) {
    $badges = [];
}

// Get recent rewards
$recentRewards = fetchAll("
    SELECT sr.*, s.first_name, s.last_name, s.student_id,
           a.username as admin_username
    FROM student_rewards sr
    JOIN students s ON sr.student_id = s.id
    LEFT JOIN admins a ON sr.awarded_by = a.id
    ORDER BY sr.created_at DESC
    LIMIT 20
");

// Ensure recentRewards is always an array
if (!is_array($recentRewards)) {
    $recentRewards = [];
}

// Get leaderboard
$leaderboard = fetchAll("
    SELECT s.*, d.name as department_name,
           (SELECT COUNT(*) FROM student_badges WHERE student_id = s.id) as badge_count
    FROM students s
    JOIN departments d ON s.department_id = d.id
    WHERE s.is_approved = 1 AND s.total_points > 0
    ORDER BY s.total_points DESC
    LIMIT 10
");

// Ensure leaderboard is always an array
if (!is_array($leaderboard)) {
    $leaderboard = [];
}

$pageTitle = 'Rewards Management';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Admin Panel</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="students.php" class="nav-item">
                    <i class="fas fa-users"></i>
                    <span>Students</span>
                </a>
                <a href="departments.php" class="nav-item">
                    <i class="fas fa-building"></i>
                    <span>Departments</span>
                </a>
                <a href="subjects.php" class="nav-item">
                    <i class="fas fa-book"></i>
                    <span>Subjects</span>
                </a>
                <a href="questions.php" class="nav-item">
                    <i class="fas fa-robot"></i>
                    <span>AI Questions</span>
                </a>
                <a href="rewards.php" class="nav-item active">
                    <i class="fas fa-trophy"></i>
                    <span>Rewards</span>
                </a>
                <a href="analytics.php" class="nav-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>Analytics</span>
                </a>
                <a href="settings.php" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>

                <div class="nav-divider"></div>

                <a href="../index.php" class="nav-item nav-secondary">
                    <i class="fas fa-home"></i>
                    <span>Back to Site</span>
                </a>
                <a href="../logout.php" class="nav-item nav-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>
            
            <div class="sidebar-footer">
                <a href="logout.php" class="nav-item logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="content-header">
                <div class="header-left">
                    <h1>Rewards Management</h1>
                    <p>Manage student rewards, points, and badges</p>
                </div>
                <div class="header-right">
                    <button class="btn btn-primary" onclick="openAwardPointsModal()">
                        <i class="fas fa-star"></i>
                        Award Points
                    </button>
                    <button class="btn btn-secondary" onclick="openCreateBadgeModal()">
                        <i class="fas fa-medal"></i>
                        Create Badge
                    </button>
                </div>
            </header>
            
            <!-- Messages -->
            <?php if (isset($success)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <!-- Rewards Overview -->
            <div class="rewards-overview">
                <div class="overview-grid">
                    <!-- Leaderboard -->
                    <div class="overview-card">
                        <div class="card-header">
                            <h3><i class="fas fa-trophy"></i> Top Students</h3>
                        </div>
                        <div class="leaderboard">
                            <?php foreach ($leaderboard as $index => $student): ?>
                                <div class="leaderboard-item">
                                    <div class="rank">
                                        <?php if ($index === 0): ?>
                                            <i class="fas fa-crown gold"></i>
                                        <?php elseif ($index === 1): ?>
                                            <i class="fas fa-medal silver"></i>
                                        <?php elseif ($index === 2): ?>
                                            <i class="fas fa-medal bronze"></i>
                                        <?php else: ?>
                                            <span class="rank-number"><?php echo $index + 1; ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="student-info">
                                        <strong><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></strong>
                                        <small><?php echo htmlspecialchars($student['department_name']); ?></small>
                                    </div>
                                    <div class="points">
                                        <span class="points-value"><?php echo $student['total_points']; ?></span>
                                        <small>points</small>
                                    </div>
                                    <div class="badges">
                                        <i class="fas fa-medal"></i>
                                        <span><?php echo $student['badge_count']; ?></span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <!-- Available Badges -->
                    <div class="overview-card">
                        <div class="card-header">
                            <h3><i class="fas fa-medal"></i> Available Badges</h3>
                        </div>
                        <div class="badges-grid">
                            <?php foreach ($badges as $badge): ?>
                                <div class="badge-item">
                                    <div class="badge-icon">
                                        <i class="<?php echo $badge['icon'] ?: 'fas fa-medal'; ?>"></i>
                                    </div>
                                    <div class="badge-info">
                                        <strong><?php echo htmlspecialchars($badge['name']); ?></strong>
                                        <small><?php echo $badge['points_required']; ?> points required</small>
                                    </div>
                                    <button class="btn-icon" onclick="awardBadgeModal(<?php echo $badge['id']; ?>)" title="Award Badge">
                                        <i class="fas fa-gift"></i>
                                    </button>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Rewards -->
            <div class="recent-rewards">
                <div class="section-header">
                    <h3>Recent Rewards</h3>
                </div>
                <div class="rewards-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Student</th>
                                <th>Points</th>
                                <th>Reason</th>
                                <th>Awarded By</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recentRewards as $reward): ?>
                                <tr>
                                    <td>
                                        <div class="student-info">
                                            <strong><?php echo htmlspecialchars($reward['first_name'] . ' ' . $reward['last_name']); ?></strong>
                                            <small><?php echo htmlspecialchars($reward['student_id']); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="points-badge">+<?php echo $reward['points_awarded']; ?></span>
                                    </td>
                                    <td><?php echo htmlspecialchars($reward['reason']); ?></td>
                                    <td><?php echo htmlspecialchars($reward['admin_username'] ?: 'System'); ?></td>
                                    <td><?php echo date('M d, Y H:i', strtotime($reward['created_at'])); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Award Points Modal -->
    <div id="awardPointsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Award Points to Student</h3>
                <span class="close" onclick="closeAwardPointsModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form method="POST">
                    <input type="hidden" name="action" value="award_points">
                    
                    <div class="form-group">
                        <label for="student_id">Select Student *</label>
                        <select id="student_id" name="student_id" required>
                            <option value="">Choose a student</option>
                            <?php foreach ($students as $student): ?>
                                <option value="<?php echo $student['id']; ?>">
                                    <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name'] . ' (' . $student['student_id'] . ')'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="points">Points to Award *</label>
                        <input type="number" id="points" name="points" min="1" max="1000" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="reason">Reason *</label>
                        <textarea id="reason" name="reason" rows="3" required placeholder="Why are you awarding these points?"></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeAwardPointsModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-star"></i>
                            Award Points
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Create Badge Modal -->
    <div id="createBadgeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create New Badge</h3>
                <span class="close" onclick="closeCreateBadgeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form method="POST">
                    <input type="hidden" name="action" value="create_badge">
                    
                    <div class="form-group">
                        <label for="badge_name">Badge Name *</label>
                        <input type="text" id="badge_name" name="name" required placeholder="e.g., Quiz Master">
                    </div>
                    
                    <div class="form-group">
                        <label for="badge_description">Description *</label>
                        <textarea id="badge_description" name="description" rows="3" required placeholder="Describe what this badge represents"></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="badge_icon">Icon (FontAwesome class)</label>
                            <input type="text" id="badge_icon" name="icon" placeholder="fas fa-medal">
                        </div>
                        
                        <div class="form-group">
                            <label for="points_required">Points Required *</label>
                            <input type="number" id="points_required" name="points_required" min="1" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="criteria">Criteria (Optional)</label>
                        <textarea id="criteria" name="criteria" rows="2" placeholder="Additional criteria for earning this badge"></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeCreateBadgeModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-medal"></i>
                            Create Badge
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="../assets/js/admin-dashboard.js"></script>
    <script src="../assets/js/rewards.js"></script>
</body>
</html>
