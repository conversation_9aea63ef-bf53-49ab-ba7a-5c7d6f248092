-- AI-Powered LMS Database Schema for Ogbonnaya Onu Polytechnic
-- Created: 2025-06-25

CREATE DATABASE IF NOT EXISTS lms_ogbonnaya_onu;
USE lms_ogbonnaya_onu;

-- Admin table for system administrators
CREATE TABLE admins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL DEFAULT 'admin',
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    is_setup BOOLEAN DEFAULT FALSE
);

-- Departments table (31 courses)
CREATE TABLE departments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(10) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Academic levels
CREATE TABLE academic_levels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    level_name VARCHAR(10) NOT NULL, -- ND1, ND2, HND1, HND2
    level_code VARCHAR(5) NOT NULL,
    description VARCHAR(100)
);

-- Security questions for password reset
CREATE TABLE security_questions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    question TEXT NOT NULL
);

-- Students table
CREATE TABLE students (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id VARCHAR(20) UNIQUE NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(15),
    password_hash VARCHAR(255) NOT NULL,
    department_id INT NOT NULL,
    academic_level_id INT NOT NULL,
    profile_image VARCHAR(255) DEFAULT 'default-avatar.png',
    is_approved BOOLEAN DEFAULT FALSE,
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    total_points INT DEFAULT 0,
    current_streak INT DEFAULT 0,
    longest_streak INT DEFAULT 0,
    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (academic_level_id) REFERENCES academic_levels(id)
);

-- Student security answers
CREATE TABLE student_security_answers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    question_id INT NOT NULL,
    answer_hash VARCHAR(255) NOT NULL,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES security_questions(id)
);

-- Subjects for each department and level
CREATE TABLE subjects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) NOT NULL,
    department_id INT NOT NULL,
    academic_level_id INT NOT NULL,
    description TEXT,
    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (academic_level_id) REFERENCES academic_levels(id)
);

-- Questions generated by AI
CREATE TABLE questions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    subject_id INT NOT NULL,
    question_text TEXT NOT NULL,
    option_a VARCHAR(255) NOT NULL,
    option_b VARCHAR(255) NOT NULL,
    option_c VARCHAR(255) NOT NULL,
    option_d VARCHAR(255) NOT NULL,
    correct_answer ENUM('A', 'B', 'C', 'D') NOT NULL,
    difficulty_level ENUM('Easy', 'Medium', 'Hard') DEFAULT 'Medium',
    explanation TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES subjects(id)
);

-- Quiz sessions
CREATE TABLE quiz_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    total_questions INT NOT NULL,
    correct_answers INT DEFAULT 0,
    score_percentage DECIMAL(5,2) DEFAULT 0.00,
    time_taken INT, -- in seconds
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    status ENUM('in_progress', 'completed', 'abandoned') DEFAULT 'in_progress',
    FOREIGN KEY (student_id) REFERENCES students(id),
    FOREIGN KEY (subject_id) REFERENCES subjects(id)
);

-- Individual question attempts
CREATE TABLE question_attempts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    quiz_session_id INT NOT NULL,
    question_id INT NOT NULL,
    selected_answer ENUM('A', 'B', 'C', 'D'),
    is_correct BOOLEAN DEFAULT FALSE,
    time_taken INT, -- in seconds
    attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (quiz_session_id) REFERENCES quiz_sessions(id),
    FOREIGN KEY (question_id) REFERENCES questions(id)
);

-- Reward types and badges
CREATE TABLE reward_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    icon VARCHAR(100), -- emoji or icon class
    points_required INT DEFAULT 0,
    badge_image VARCHAR(255)
);

-- Student rewards and achievements
CREATE TABLE student_rewards (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    reward_type_id INT NOT NULL,
    points_earned INT DEFAULT 0,
    earned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    quiz_session_id INT,
    FOREIGN KEY (student_id) REFERENCES students(id),
    FOREIGN KEY (reward_type_id) REFERENCES reward_types(id),
    FOREIGN KEY (quiz_session_id) REFERENCES quiz_sessions(id)
);

-- Student progress tracking
CREATE TABLE student_progress (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    questions_attempted INT DEFAULT 0,
    questions_correct INT DEFAULT 0,
    average_score DECIMAL(5,2) DEFAULT 0.00,
    time_spent INT DEFAULT 0, -- total time in seconds
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    mastery_level ENUM('Beginner', 'Intermediate', 'Advanced', 'Expert') DEFAULT 'Beginner',
    FOREIGN KEY (student_id) REFERENCES students(id),
    FOREIGN KEY (subject_id) REFERENCES subjects(id),
    UNIQUE KEY unique_student_subject (student_id, subject_id)
);

-- System settings
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Session management
CREATE TABLE user_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL,
    user_type ENUM('student', 'admin') NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE
);
