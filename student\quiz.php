<?php
/**
 * Student Quiz Interface for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Check if student is logged in
startSecureSession();
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'student') {
    header('Location: login.php');
    exit();
}

// Get student information
$student = fetchOne("
    SELECT s.*, d.name as department_name, al.level_name
    FROM students s
    JOIN departments d ON s.department_id = d.id
    JOIN academic_levels al ON s.academic_level_id = al.id
    WHERE s.id = :id
", ['id' => $_SESSION['user_id']]);

if (!$student) {
    session_destroy();
    header('Location: login.php');
    exit();
}

// Handle quiz actions
$action = $_GET['action'] ?? 'start';
$quizSessionId = $_GET['quiz_id'] ?? null;
$resumeQuizId = $_GET['resume'] ?? null;

// Check for existing in-progress quiz
$existingQuiz = fetchOne("
    SELECT * FROM quiz_sessions 
    WHERE student_id = :student_id AND status = 'in_progress'
    ORDER BY created_at DESC LIMIT 1
", ['student_id' => $student['id']]);

if ($action === 'start' && !$resumeQuizId) {
    // Starting a new quiz
    if ($existingQuiz) {
        // Redirect to existing quiz
        header("Location: quiz.php?action=take&quiz_id=" . $existingQuiz['id']);
        exit();
    }
    
    // Generate new quiz questions using AI
    $questionsGenerated = generateQuizQuestions($student);
    
    if ($questionsGenerated['success']) {
        // Create new quiz session
        $quizSessionId = insertRecord('quiz_sessions', [
            'student_id' => $student['id'],
            'department_id' => $student['department_id'],
            'academic_level_id' => $student['academic_level_id'],
            'total_questions' => $questionsGenerated['count'],
            'status' => 'in_progress',
            'started_at' => date('Y-m-d H:i:s')
        ]);
        
        if ($quizSessionId) {
            // Store questions for this quiz session
            foreach ($questionsGenerated['questions'] as $index => $question) {
                insertRecord('quiz_questions', [
                    'quiz_session_id' => $quizSessionId,
                    'question_id' => $question['id'],
                    'question_number' => $index + 1
                ]);
            }
            
            header("Location: quiz.php?action=take&quiz_id=$quizSessionId");
            exit();
        } else {
            $error = "Failed to create quiz session. Please try again.";
        }
    } else {
        $error = $questionsGenerated['error'] ?? "Failed to generate questions. Please try again.";
    }
}

if ($action === 'take') {
    // Taking/continuing a quiz
    if (!$quizSessionId) {
        header('Location: quiz.php');
        exit();
    }
    
    // Get quiz session
    $quizSession = fetchOne("
        SELECT * FROM quiz_sessions 
        WHERE id = :id AND student_id = :student_id
    ", ['id' => $quizSessionId, 'student_id' => $student['id']]);
    
    if (!$quizSession || $quizSession['status'] === 'completed') {
        header('Location: quiz.php');
        exit();
    }
    
    // Get quiz questions
    $quizQuestions = fetchAll("
        SELECT qq.*, q.question_text, q.option_a, q.option_b, q.option_c, q.option_d, q.difficulty
        FROM quiz_questions qq
        JOIN questions q ON qq.question_id = q.id
        WHERE qq.quiz_session_id = :quiz_id
        ORDER BY qq.question_number
    ", ['quiz_id' => $quizSessionId]);
    
    // Get current answers
    $currentAnswers = fetchAll("
        SELECT * FROM quiz_answers 
        WHERE quiz_session_id = :quiz_id
    ", ['quiz_id' => $quizSessionId]);
    
    $answersMap = [];
    foreach ($currentAnswers as $answer) {
        $answersMap[$answer['question_number']] = $answer['selected_answer'];
    }
    
    $currentQuestion = count($currentAnswers) + 1;
    if ($currentQuestion > count($quizQuestions)) {
        $currentQuestion = count($quizQuestions);
    }
}

/**
 * Generate quiz questions using AI for student's department and level
 */
function generateQuizQuestions($student) {
    try {
        // Check if questions already exist for this department and level
        $existingQuestions = fetchAll("
            SELECT * FROM questions 
            WHERE department_id = :dept_id AND academic_level_id = :level_id
            ORDER BY RAND()
            LIMIT 10
        ", [
            'dept_id' => $student['department_id'],
            'level_id' => $student['academic_level_id']
        ]);
        
        if (count($existingQuestions) >= 5) {
            // Use existing questions
            return [
                'success' => true,
                'questions' => array_slice($existingQuestions, 0, 10),
                'count' => min(10, count($existingQuestions)),
                'source' => 'existing'
            ];
        }
        
        // Generate new questions using AI
        $department = fetchOne("SELECT * FROM departments WHERE id = :id", ['id' => $student['department_id']]);
        $academicLevel = fetchOne("SELECT * FROM academic_levels WHERE id = :id", ['id' => $student['academic_level_id']]);
        
        if (!$department || !$academicLevel) {
            return ['success' => false, 'error' => 'Invalid department or academic level'];
        }
        
        // Call AI generation function
        $apiKey = "********************************************************************************************************************************************************************";
        $generatedQuestions = callOpenAIForQuestions($department, $academicLevel, 10, $apiKey);
        
        if (!empty($generatedQuestions)) {
            // Save generated questions to database
            $savedQuestions = [];
            foreach ($generatedQuestions as $q) {
                $questionId = insertRecord('questions', [
                    'department_id' => $student['department_id'],
                    'academic_level_id' => $student['academic_level_id'],
                    'question_text' => $q['question_text'],
                    'option_a' => $q['option_a'],
                    'option_b' => $q['option_b'],
                    'option_c' => $q['option_c'],
                    'option_d' => $q['option_d'],
                    'correct_answer' => $q['correct_answer'],
                    'explanation' => $q['explanation'] ?? '',
                    'difficulty' => $q['difficulty'] ?? 'medium'
                ]);
                
                if ($questionId) {
                    $savedQuestions[] = array_merge($q, ['id' => $questionId]);
                }
            }
            
            return [
                'success' => true,
                'questions' => $savedQuestions,
                'count' => count($savedQuestions),
                'source' => 'generated'
            ];
        }
        
        return ['success' => false, 'error' => 'Failed to generate questions with AI'];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Call OpenAI API to generate questions
 */
function callOpenAIForQuestions($department, $academicLevel, $numQuestions, $apiKey) {
    $prompt = "Generate $numQuestions multiple-choice questions for {$department['name']} students at {$academicLevel['level_name']} level. 

Focus on practical and theoretical knowledge relevant to {$department['name']} curriculum at polytechnic level.

Return ONLY a valid JSON array with this exact structure:
[
  {
    \"question_text\": \"Question here?\",
    \"option_a\": \"First option\",
    \"option_b\": \"Second option\", 
    \"option_c\": \"Third option\",
    \"option_d\": \"Fourth option\",
    \"correct_answer\": \"A\",
    \"explanation\": \"Why this answer is correct\",
    \"difficulty\": \"medium\"
  }
]

Make questions diverse, practical, and appropriate for polytechnic {$academicLevel['level_name']} students studying {$department['name']}.";

    $data = [
        'model' => 'gpt-3.5-turbo',
        'messages' => [
            [
                'role' => 'system',
                'content' => 'You are an expert educational content creator. Return only valid JSON arrays of questions. No additional text or formatting.'
            ],
            [
                'role' => 'user',
                'content' => $prompt
            ]
        ],
        'max_tokens' => 3000,
        'temperature' => 0.7
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/chat/completions');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $apiKey
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode === 200 && $response) {
        $result = json_decode($response, true);
        if (isset($result['choices'][0]['message']['content'])) {
            $content = trim($result['choices'][0]['message']['content']);
            $questions = json_decode($content, true);
            return is_array($questions) ? $questions : [];
        }
    }
    
    return [];
}

$pageTitle = 'Take Quiz';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Quiz-specific styling */
        .quiz-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .quiz-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        .quiz-progress {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }
        .question-card {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .question-number {
            color: #28a745;
            font-weight: 600;
            margin-bottom: 10px;
        }
        .question-text {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 25px;
            line-height: 1.6;
        }
        .option {
            display: block;
            width: 100%;
            padding: 15px 20px;
            margin-bottom: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
        }
        .option:hover {
            border-color: #28a745;
            background: #f8f9fa;
        }
        .option.selected {
            border-color: #28a745;
            background: #d4edda;
            color: #155724;
        }
        .quiz-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
        }
        .btn-quiz {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-quiz:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .quiz-timer {
            font-size: 18px;
            font-weight: 600;
            color: #dc3545;
        }
        .start-quiz-container {
            text-align: center;
            padding: 50px 20px;
        }
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Student Portal</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>

            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="quiz.php" class="nav-item active">
                    <i class="fas fa-brain"></i>
                    <span>Take Quiz</span>
                </a>
                <a href="results.php" class="nav-item">
                    <i class="fas fa-chart-line"></i>
                    <span>My Results</span>
                </a>
                <a href="profile.php" class="nav-item">
                    <i class="fas fa-user"></i>
                    <span>Profile</span>
                </a>
                <a href="logout.php" class="nav-item">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <h4><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h4>
                        <p><?php echo htmlspecialchars($student['student_id']); ?></p>
                        <p><?php echo htmlspecialchars($student['department_name'] . ' - ' . $student['level_name']); ?></p>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="quiz-container">
                <?php if (isset($error)): ?>
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <?php if ($action === 'start' || (!isset($quizSession))): ?>
                    <!-- Start Quiz Screen -->
                    <div class="quiz-header">
                        <h1><i class="fas fa-brain"></i> AI-Generated Quiz</h1>
                        <p>Test your knowledge with personalized questions for <?php echo htmlspecialchars($student['department_name'] . ' - ' . $student['level_name']); ?></p>
                    </div>

                    <div class="start-quiz-container">
                        <div class="question-card">
                            <h2>Ready to Start Your Quiz?</h2>
                            <p>This quiz will contain 10 AI-generated questions specifically tailored to your course and academic level.</p>

                            <div style="margin: 30px 0;">
                                <div style="display: flex; justify-content: space-around; text-align: center;">
                                    <div>
                                        <i class="fas fa-clock" style="font-size: 24px; color: #28a745; margin-bottom: 10px;"></i>
                                        <p><strong>No Time Limit</strong><br>Take your time to think</p>
                                    </div>
                                    <div>
                                        <i class="fas fa-question-circle" style="font-size: 24px; color: #28a745; margin-bottom: 10px;"></i>
                                        <p><strong>10 Questions</strong><br>Multiple choice format</p>
                                    </div>
                                    <div>
                                        <i class="fas fa-robot" style="font-size: 24px; color: #28a745; margin-bottom: 10px;"></i>
                                        <p><strong>AI Generated</strong><br>Personalized for you</p>
                                    </div>
                                </div>
                            </div>

                            <?php if ($existingQuiz): ?>
                                <div class="success-message">
                                    <i class="fas fa-info-circle"></i>
                                    You have an in-progress quiz. You can continue where you left off.
                                </div>
                                <a href="quiz.php?action=take&quiz_id=<?php echo $existingQuiz['id']; ?>" class="btn-quiz btn-primary">
                                    <i class="fas fa-play"></i> Continue Existing Quiz
                                </a>
                            <?php else: ?>
                                <a href="quiz.php?action=start" class="btn-quiz btn-primary">
                                    <i class="fas fa-play"></i> Start New Quiz
                                </a>
                            <?php endif; ?>

                            <br><br>
                            <a href="dashboard.php" class="btn-quiz btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($action === 'take' && isset($quizSession) && !empty($quizQuestions)): ?>
                    <!-- Quiz Taking Interface -->
                    <div class="quiz-header">
                        <h1><i class="fas fa-brain"></i> Quiz in Progress</h1>
                        <p><?php echo htmlspecialchars($student['department_name'] . ' - ' . $student['level_name']); ?></p>
                    </div>

                    <div class="quiz-progress">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <span><strong>Question <?php echo $currentQuestion; ?> of <?php echo count($quizQuestions); ?></strong></span>
                            <span class="quiz-timer" id="timer">Time: <span id="time-display">00:00</span></span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: <?php echo ($currentQuestion / count($quizQuestions)) * 100; ?>%"></div>
                        </div>
                    </div>

                    <form id="quizForm" method="POST" action="quiz-submit.php">
                        <input type="hidden" name="quiz_session_id" value="<?php echo $quizSession['id']; ?>">
                        <input type="hidden" name="current_question" value="<?php echo $currentQuestion; ?>">

                        <?php
                        $question = $quizQuestions[$currentQuestion - 1];
                        $selectedAnswer = $answersMap[$currentQuestion] ?? '';
                        ?>

                        <div class="question-card">
                            <div class="question-number">
                                Question <?php echo $currentQuestion; ?>
                                <span style="float: right; color: #6c757d; font-size: 14px;">
                                    Difficulty: <?php echo ucfirst($question['difficulty']); ?>
                                </span>
                            </div>

                            <div class="question-text">
                                <?php echo htmlspecialchars($question['question_text']); ?>
                            </div>

                            <div class="options">
                                <label class="option <?php echo $selectedAnswer === 'A' ? 'selected' : ''; ?>">
                                    <input type="radio" name="answer" value="A" <?php echo $selectedAnswer === 'A' ? 'checked' : ''; ?> style="display: none;">
                                    <strong>A.</strong> <?php echo htmlspecialchars($question['option_a']); ?>
                                </label>

                                <label class="option <?php echo $selectedAnswer === 'B' ? 'selected' : ''; ?>">
                                    <input type="radio" name="answer" value="B" <?php echo $selectedAnswer === 'B' ? 'checked' : ''; ?> style="display: none;">
                                    <strong>B.</strong> <?php echo htmlspecialchars($question['option_b']); ?>
                                </label>

                                <label class="option <?php echo $selectedAnswer === 'C' ? 'selected' : ''; ?>">
                                    <input type="radio" name="answer" value="C" <?php echo $selectedAnswer === 'C' ? 'checked' : ''; ?> style="display: none;">
                                    <strong>C.</strong> <?php echo htmlspecialchars($question['option_c']); ?>
                                </label>

                                <label class="option <?php echo $selectedAnswer === 'D' ? 'selected' : ''; ?>">
                                    <input type="radio" name="answer" value="D" <?php echo $selectedAnswer === 'D' ? 'checked' : ''; ?> style="display: none;">
                                    <strong>D.</strong> <?php echo htmlspecialchars($question['option_d']); ?>
                                </label>
                            </div>

                            <div class="quiz-navigation">
                                <div>
                                    <?php if ($currentQuestion > 1): ?>
                                        <button type="button" onclick="navigateQuestion(<?php echo $currentQuestion - 1; ?>)" class="btn-quiz btn-secondary">
                                            <i class="fas fa-arrow-left"></i> Previous
                                        </button>
                                    <?php endif; ?>
                                </div>

                                <div>
                                    <?php if ($currentQuestion < count($quizQuestions)): ?>
                                        <button type="submit" name="action" value="next" class="btn-quiz btn-primary">
                                            Next <i class="fas fa-arrow-right"></i>
                                        </button>
                                    <?php else: ?>
                                        <button type="submit" name="action" value="finish" class="btn-quiz btn-primary" onclick="return confirm('Are you sure you want to submit your quiz? You cannot change your answers after submission.')">
                                            <i class="fas fa-check"></i> Submit Quiz
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Question Navigation -->
                    <div class="question-card">
                        <h4>Question Navigation</h4>
                        <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 15px;">
                            <?php for ($i = 1; $i <= count($quizQuestions); $i++): ?>
                                <button type="button"
                                        onclick="navigateQuestion(<?php echo $i; ?>)"
                                        class="btn-quiz <?php echo $i === $currentQuestion ? 'btn-primary' : (isset($answersMap[$i]) ? 'btn-secondary' : 'btn-outline'); ?>"
                                        style="width: 40px; height: 40px; padding: 0;">
                                    <?php echo $i; ?>
                                </button>
                            <?php endfor; ?>
                        </div>
                        <p style="margin-top: 15px; color: #6c757d; font-size: 14px;">
                            <i class="fas fa-info-circle"></i>
                            Click on any question number to jump to that question.
                            Answered questions are highlighted.
                        </p>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <script>
        // Quiz functionality
        let startTime = new Date();

        // Timer functionality
        function updateTimer() {
            const now = new Date();
            const elapsed = Math.floor((now - startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            document.getElementById('time-display').textContent =
                String(minutes).padStart(2, '0') + ':' + String(seconds).padStart(2, '0');
        }

        if (document.getElementById('timer')) {
            setInterval(updateTimer, 1000);
        }

        // Option selection
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                // Remove selected class from all options
                document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
                // Add selected class to clicked option
                this.classList.add('selected');
                // Check the radio button
                this.querySelector('input[type="radio"]').checked = true;
            });
        });

        // Question navigation
        function navigateQuestion(questionNumber) {
            const form = document.getElementById('quizForm');
            if (form) {
                // Save current answer first
                const selectedAnswer = document.querySelector('input[name="answer"]:checked');
                if (selectedAnswer) {
                    // Submit current answer via AJAX
                    const formData = new FormData(form);
                    formData.append('action', 'save');
                    formData.append('navigate_to', questionNumber);

                    fetch('quiz-submit.php', {
                        method: 'POST',
                        body: formData
                    }).then(() => {
                        // Navigate to the question
                        window.location.href = `quiz.php?action=take&quiz_id=${form.quiz_session_id.value}&question=${questionNumber}`;
                    });
                } else {
                    // Navigate without saving
                    window.location.href = `quiz.php?action=take&quiz_id=${form.quiz_session_id.value}&question=${questionNumber}`;
                }
            }
        }

        // Auto-save functionality
        let autoSaveTimeout;
        document.querySelectorAll('input[name="answer"]').forEach(input => {
            input.addEventListener('change', function() {
                clearTimeout(autoSaveTimeout);
                autoSaveTimeout = setTimeout(() => {
                    const form = document.getElementById('quizForm');
                    const formData = new FormData(form);
                    formData.append('action', 'save');

                    fetch('quiz-submit.php', {
                        method: 'POST',
                        body: formData
                    });
                }, 1000);
            });
        });

        // Prevent accidental page refresh
        window.addEventListener('beforeunload', function(e) {
            if (document.getElementById('quizForm')) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
    </script>
</body>
</html>
