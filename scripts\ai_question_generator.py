#!/usr/bin/env python3
"""
AI Question Generator for LMS
Ogbonnaya Onu Polytechnic, Aba

This script generates department-specific multiple-choice questions using AI.
It connects to the LMS database to get API keys and department information.
"""

import os
import sys
import json
import mysql.connector
from mysql.connector import <PERSON>rror
import openai
from typing import List, Dict, Any
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ai_generator.log'),
        logging.StreamHandler()
    ]
)

class AIQuestionGenerator:
    def __init__(self):
        """Initialize the AI Question Generator"""
        self.db_config = {
            'host': 'localhost',
            'database': 'lms_db',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4'
        }
        self.connection = None
        self.api_key = None
        self.ai_model = 'gpt-3.5-turbo'
        self.question_complexity = 'medium'
        
    def connect_database(self):
        """Connect to the MySQL database"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            if self.connection.is_connected():
                logging.info("Successfully connected to MySQL database")
                return True
        except Error as e:
            logging.error(f"Error connecting to MySQL: {e}")
            return False
    
    def get_ai_settings(self):
        """Retrieve AI settings from the database"""
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            # Get AI configuration settings
            settings_query = """
                SELECT setting_key, setting_value 
                FROM system_settings 
                WHERE setting_key IN ('ai_api_key', 'ai_model', 'question_complexity', 'ai_provider')
            """
            cursor.execute(settings_query)
            settings = cursor.fetchall()
            
            # Process settings
            for setting in settings:
                if setting['setting_key'] == 'ai_api_key':
                    self.api_key = setting['setting_value']
                elif setting['setting_key'] == 'ai_model':
                    self.ai_model = setting['setting_value']
                elif setting['setting_key'] == 'question_complexity':
                    self.question_complexity = setting['setting_value']
            
            cursor.close()
            
            if not self.api_key:
                logging.error("No API key found in database. Please configure AI settings first.")
                return False
                
            # Configure OpenAI
            openai.api_key = self.api_key
            logging.info(f"AI settings loaded: Model={self.ai_model}, Complexity={self.question_complexity}")
            return True
            
        except Error as e:
            logging.error(f"Error retrieving AI settings: {e}")
            return False
    
    def get_departments(self):
        """Get all departments from the database"""
        try:
            cursor = self.connection.cursor(dictionary=True)
            query = "SELECT id, name, code, description FROM departments WHERE status = 'active'"
            cursor.execute(query)
            departments = cursor.fetchall()
            cursor.close()
            return departments
        except Error as e:
            logging.error(f"Error retrieving departments: {e}")
            return []
    
    def get_subjects_for_department(self, department_id: int):
        """Get subjects for a specific department"""
        try:
            cursor = self.connection.cursor(dictionary=True)
            query = """
                SELECT id, name, code, description 
                FROM subjects 
                WHERE department_id = %s AND status = 'active'
            """
            cursor.execute(query, (department_id,))
            subjects = cursor.fetchall()
            cursor.close()
            return subjects
        except Error as e:
            logging.error(f"Error retrieving subjects for department {department_id}: {e}")
            return []
    
    def generate_questions_prompt(self, department: Dict, subject: Dict, num_questions: int = 10):
        """Generate a prompt for AI question generation"""
        complexity_descriptions = {
            'basic': 'fundamental concepts and basic understanding',
            'medium': 'intermediate concepts requiring analysis and application',
            'advanced': 'complex concepts requiring critical thinking and synthesis',
            'mixed': 'a mix of basic, intermediate, and advanced concepts'
        }
        
        prompt = f"""
Generate {num_questions} multiple-choice questions for the following educational context:

Department: {department['name']} ({department['code']})
Subject: {subject['name']} ({subject['code']})
Subject Description: {subject['description']}
Department Description: {department['description']}

Question Requirements:
- Complexity Level: {self.question_complexity} ({complexity_descriptions.get(self.question_complexity, 'medium level')})
- Each question should have 4 options (A, B, C, D)
- Questions should cover a wide and deep scope of the subject
- Questions should be relevant to polytechnic-level education
- Include practical, theoretical, and application-based questions
- Ensure questions are clear, unambiguous, and educationally valuable

Please return the questions in the following JSON format:
{{
    "questions": [
        {{
            "question": "Question text here?",
            "options": {{
                "A": "Option A text",
                "B": "Option B text", 
                "C": "Option C text",
                "D": "Option D text"
            }},
            "correct_answer": "A",
            "explanation": "Brief explanation of why this is correct",
            "difficulty": "easy|medium|hard",
            "topic": "Specific topic within the subject"
        }}
    ]
}}

Generate diverse questions covering different aspects of {subject['name']} that would be appropriate for polytechnic students.
"""
        return prompt
    
    def call_openai_api(self, prompt: str):
        """Call OpenAI API to generate questions"""
        try:
            response = openai.ChatCompletion.create(
                model=self.ai_model,
                messages=[
                    {"role": "system", "content": "You are an expert educational content creator specializing in polytechnic-level curriculum. Generate high-quality, diverse multiple-choice questions that test both theoretical knowledge and practical application."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=3000,
                temperature=0.7
            )
            
            content = response.choices[0].message.content.strip()
            
            # Try to parse JSON response
            try:
                questions_data = json.loads(content)
                return questions_data.get('questions', [])
            except json.JSONDecodeError:
                logging.error("Failed to parse JSON response from OpenAI")
                return []
                
        except Exception as e:
            logging.error(f"Error calling OpenAI API: {e}")
            return []
    
    def save_questions_to_database(self, questions: List[Dict], department_id: int, subject_id: int):
        """Save generated questions to the database"""
        try:
            cursor = self.connection.cursor()
            
            saved_count = 0
            for question_data in questions:
                # Insert question
                question_query = """
                    INSERT INTO questions (
                        department_id, subject_id, question_text, option_a, option_b, 
                        option_c, option_d, correct_answer, explanation, difficulty, 
                        topic, source, created_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                """
                
                question_values = (
                    department_id,
                    subject_id,
                    question_data.get('question', ''),
                    question_data.get('options', {}).get('A', ''),
                    question_data.get('options', {}).get('B', ''),
                    question_data.get('options', {}).get('C', ''),
                    question_data.get('options', {}).get('D', ''),
                    question_data.get('correct_answer', 'A'),
                    question_data.get('explanation', ''),
                    question_data.get('difficulty', 'medium'),
                    question_data.get('topic', ''),
                    'AI Generated'
                )
                
                cursor.execute(question_query, question_values)
                saved_count += 1
            
            self.connection.commit()
            cursor.close()
            
            logging.info(f"Successfully saved {saved_count} questions to database")
            return saved_count
            
        except Error as e:
            logging.error(f"Error saving questions to database: {e}")
            self.connection.rollback()
            return 0
    
    def generate_questions_for_department(self, department_id: int, questions_per_subject: int = 10):
        """Generate questions for all subjects in a department"""
        try:
            # Get department info
            cursor = self.connection.cursor(dictionary=True)
            dept_query = "SELECT * FROM departments WHERE id = %s"
            cursor.execute(dept_query, (department_id,))
            department = cursor.fetchone()
            cursor.close()
            
            if not department:
                logging.error(f"Department with ID {department_id} not found")
                return 0
            
            # Get subjects for this department
            subjects = self.get_subjects_for_department(department_id)
            
            if not subjects:
                logging.warning(f"No subjects found for department: {department['name']}")
                return 0
            
            total_generated = 0
            
            for subject in subjects:
                logging.info(f"Generating questions for {subject['name']} in {department['name']}")
                
                # Generate prompt
                prompt = self.generate_questions_prompt(department, subject, questions_per_subject)
                
                # Call AI API
                questions = self.call_openai_api(prompt)
                
                if questions:
                    # Save to database
                    saved_count = self.save_questions_to_database(questions, department_id, subject['id'])
                    total_generated += saved_count
                    logging.info(f"Generated {saved_count} questions for {subject['name']}")
                else:
                    logging.warning(f"No questions generated for {subject['name']}")
            
            return total_generated
            
        except Exception as e:
            logging.error(f"Error generating questions for department {department_id}: {e}")
            return 0
    
    def generate_questions_for_all_departments(self, questions_per_subject: int = 10):
        """Generate questions for all departments"""
        departments = self.get_departments()
        
        if not departments:
            logging.error("No departments found")
            return 0
        
        total_generated = 0
        
        for department in departments:
            logging.info(f"Processing department: {department['name']}")
            generated = self.generate_questions_for_department(department['id'], questions_per_subject)
            total_generated += generated
        
        return total_generated
    
    def close_connection(self):
        """Close database connection"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logging.info("Database connection closed")

def main():
    """Main function to run the AI question generator"""
    generator = AIQuestionGenerator()
    
    try:
        # Connect to database
        if not generator.connect_database():
            sys.exit(1)
        
        # Get AI settings
        if not generator.get_ai_settings():
            sys.exit(1)
        
        # Generate questions
        logging.info("Starting AI question generation...")
        total_questions = generator.generate_questions_for_all_departments(questions_per_subject=5)
        
        if total_questions > 0:
            logging.info(f"Successfully generated {total_questions} questions!")
        else:
            logging.warning("No questions were generated")
    
    except KeyboardInterrupt:
        logging.info("Question generation interrupted by user")
    except Exception as e:
        logging.error(f"Unexpected error: {e}")
    finally:
        generator.close_connection()

if __name__ == "__main__":
    main()
