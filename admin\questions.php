<?php
/**
 * AI Questions Management for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 * 
 * This page manages AI-generated questions, not manual question creation
 */

require_once '../config/database.php';

// Require admin login
requireLogin('admin');

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'generate_questions':
                    $subject_id = (int)$_POST['subject_id'];
                    $difficulty = $_POST['difficulty'];
                    $count = (int)$_POST['count'];
                    $topics = $_POST['topics'] ?? '';
                    
                    if (!$subject_id || !$difficulty || !$count) {
                        throw new Exception('Subject, difficulty, and count are required');
                    }
                    
                    if ($count < 1 || $count > 100) {
                        throw new Exception('Question count must be between 1 and 100');
                    }
                    
                    // Get subject details
                    $subject = fetchOne("
                        SELECT s.*, d.name as department_name, al.level_name 
                        FROM subjects s 
                        JOIN departments d ON s.department_id = d.id 
                        JOIN academic_levels al ON s.academic_level_id = al.id 
                        WHERE s.id = :id
                    ", ['id' => $subject_id]);
                    
                    if (!$subject) {
                        throw new Exception('Subject not found');
                    }
                    
                    // Call AI question generation API
                    $generationResult = generateAIQuestions($subject, $difficulty, $count, $topics);
                    
                    if ($generationResult['success']) {
                        $success = "Successfully generated {$generationResult['generated_count']} questions for {$subject['name']}!";
                    } else {
                        throw new Exception($generationResult['error']);
                    }
                    break;
                    
                case 'delete_questions':
                    $subject_id = (int)$_POST['subject_id'];
                    $difficulty = $_POST['difficulty'] ?? '';
                    
                    $whereClause = "subject_id = :subject_id";
                    $params = ['subject_id' => $subject_id];
                    
                    if ($difficulty) {
                        $whereClause .= " AND difficulty = :difficulty";
                        $params['difficulty'] = $difficulty;
                    }
                    
                    $deletedCount = execute("DELETE FROM questions WHERE $whereClause", $params);
                    $success = "Deleted $deletedCount questions successfully!";
                    break;
                    
                case 'regenerate_questions':
                    $subject_id = (int)$_POST['subject_id'];
                    $difficulty = $_POST['difficulty'];
                    
                    // Delete existing questions
                    execute("DELETE FROM questions WHERE subject_id = :subject_id AND difficulty = :difficulty", [
                        'subject_id' => $subject_id,
                        'difficulty' => $difficulty
                    ]);
                    
                    // Generate new questions
                    $subject = fetchOne("
                        SELECT s.*, d.name as department_name, al.level_name 
                        FROM subjects s 
                        JOIN departments d ON s.department_id = d.id 
                        JOIN academic_levels al ON s.academic_level_id = al.id 
                        WHERE s.id = :id
                    ", ['id' => $subject_id]);
                    
                    $generationResult = generateAIQuestions($subject, $difficulty, 20, ''); // Default 20 questions
                    
                    if ($generationResult['success']) {
                        $success = "Successfully regenerated {$generationResult['generated_count']} questions!";
                    } else {
                        throw new Exception($generationResult['error']);
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get filter parameters
$subject_filter = $_GET['subject'] ?? '';
$difficulty_filter = $_GET['difficulty'] ?? '';

// Build query conditions
$conditions = [];
$params = [];

if ($subject_filter) {
    $conditions[] = "q.subject_id = :subject";
    $params['subject'] = $subject_filter;
}

if ($difficulty_filter) {
    $conditions[] = "q.difficulty = :difficulty";
    $params['difficulty'] = $difficulty_filter;
}

$whereClause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';

// Get questions with department and level info
$questions = fetchAll("
    SELECT q.*, d.name as department_name, al.level_name
    FROM questions q
    JOIN departments d ON q.department_id = d.id
    JOIN academic_levels al ON q.academic_level_id = al.id
    $whereClause
    ORDER BY d.name, al.level_name, q.difficulty, q.created_at DESC
    LIMIT 100
", $params);

// Ensure $questions is always an array
if (!$questions) {
    $questions = [];
}

// Get departments and levels for filters and generation
$departments = fetchAll("
    SELECT d.*, COUNT(q.id) as question_count
    FROM departments d
    LEFT JOIN questions q ON d.id = q.department_id
    GROUP BY d.id
    ORDER BY d.name
");

// Ensure $departments is always an array
if (!$departments) {
    $departments = [];
}

// Get statistics
$totalQuestions = fetchOne("SELECT COUNT(*) as count FROM questions")['count'];
$questionsByDifficulty = fetchAll("
    SELECT difficulty, COUNT(*) as count
    FROM questions
    GROUP BY difficulty
    ORDER BY difficulty
");

// Ensure $questionsByDifficulty is always an array
if (!$questionsByDifficulty) {
    $questionsByDifficulty = [];
}

$pageTitle = 'AI Questions Management';

/**
 * Generate AI questions for a subject
 */
function generateAIQuestions($subject, $difficulty, $count, $topics) {
    try {
        // This would call your Python AI service
        // For now, we'll simulate the generation
        
        $prompt = buildQuestionPrompt($subject, $difficulty, $count, $topics);
        
        // Call Python AI service (placeholder)
        $aiResponse = callPythonAIService($prompt, $subject, $difficulty, $count);
        
        if ($aiResponse['success']) {
            // Save generated questions to database
            $savedCount = saveGeneratedQuestions($aiResponse['questions'], $subject['id']);
            
            return [
                'success' => true,
                'generated_count' => $savedCount
            ];
        } else {
            return [
                'success' => false,
                'error' => $aiResponse['error']
            ];
        }
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

function buildQuestionPrompt($subject, $difficulty, $count, $topics) {
    $prompt = "Generate $count multiple-choice questions for:\n";
    $prompt .= "Subject: {$subject['name']} ({$subject['code']})\n";
    $prompt .= "Department: {$subject['department_name']}\n";
    $prompt .= "Level: {$subject['level_name']}\n";
    $prompt .= "Difficulty: $difficulty\n";
    
    if ($topics) {
        $prompt .= "Topics: $topics\n";
    }
    
    $prompt .= "\nRequirements:\n";
    $prompt .= "- Each question should have 4 options (A, B, C, D)\n";
    $prompt .= "- Questions should be relevant to the subject and academic level\n";
    $prompt .= "- Difficulty should be appropriate for $difficulty level\n";
    $prompt .= "- Include diverse question types and topics\n";
    $prompt .= "- Ensure questions are clear and unambiguous\n";
    
    return $prompt;
}

function callPythonAIService($prompt, $subject, $difficulty, $count) {
    // Try to call the actual Python AI service
    try {
        $pythonScript = '../ai-service/question_generator.py';

        if (file_exists($pythonScript)) {
            // Call Python script with parameters
            $command = "python \"$pythonScript\" \"" . addslashes($subject['name']) . "\" \"$difficulty\" \"$count\"";
            $output = shell_exec($command);

            if ($output) {
                $response = json_decode($output, true);
                if ($response && $response['success']) {
                    return $response;
                }
            }
        }

        // Fallback to sample questions if Python service fails
        return [
            'success' => true,
            'questions' => generateSampleQuestions($subject, $difficulty, $count),
            'note' => 'Using sample questions - Python AI service not available'
        ];

    } catch (Exception $e) {
        // Fallback to sample questions on error
        return [
            'success' => true,
            'questions' => generateSampleQuestions($subject, $difficulty, $count),
            'note' => 'Using sample questions - Error: ' . $e->getMessage()
        ];
    }
}

function generateSampleQuestions($subject, $difficulty, $count) {
    // Sample questions for demonstration
    $sampleQuestions = [];
    
    for ($i = 1; $i <= $count; $i++) {
        $sampleQuestions[] = [
            'question_text' => "Sample {$subject['name']} question $i for {$difficulty} level",
            'option_a' => "Option A for question $i",
            'option_b' => "Option B for question $i", 
            'option_c' => "Option C for question $i",
            'option_d' => "Option D for question $i",
            'correct_answer' => ['A', 'B', 'C', 'D'][rand(0, 3)],
            'explanation' => "Explanation for question $i",
            'difficulty' => $difficulty
        ];
    }
    
    return $sampleQuestions;
}

function saveGeneratedQuestions($questions, $subjectId) {
    $savedCount = 0;
    
    foreach ($questions as $question) {
        try {
            execute("
                INSERT INTO questions (
                    subject_id, question_text, option_a, option_b, option_c, option_d,
                    correct_answer, explanation, difficulty, created_at
                ) VALUES (
                    :subject_id, :question_text, :option_a, :option_b, :option_c, :option_d,
                    :correct_answer, :explanation, :difficulty, NOW()
                )
            ", [
                'subject_id' => $subjectId,
                'question_text' => $question['question_text'],
                'option_a' => $question['option_a'],
                'option_b' => $question['option_b'],
                'option_c' => $question['option_c'],
                'option_d' => $question['option_d'],
                'correct_answer' => $question['correct_answer'],
                'explanation' => $question['explanation'],
                'difficulty' => $question['difficulty']
            ]);
            
            $savedCount++;
        } catch (Exception $e) {
            error_log("Failed to save question: " . $e->getMessage());
        }
    }
    
    return $savedCount;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Admin Panel</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="students.php" class="nav-item">
                    <i class="fas fa-users"></i>
                    <span>Students</span>
                </a>
                <a href="departments.php" class="nav-item">
                    <i class="fas fa-building"></i>
                    <span>Departments</span>
                </a>
                <a href="questions.php" class="nav-item active">
                    <i class="fas fa-robot"></i>
                    <span>AI Questions</span>
                </a>
                <a href="generate-questions.php" class="nav-item">
                    <i class="fas fa-magic"></i>
                    <span>Generate Questions</span>
                </a>
                <a href="rewards.php" class="nav-item">
                    <i class="fas fa-trophy"></i>
                    <span>Rewards</span>
                </a>
                <a href="analytics.php" class="nav-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>Analytics</span>
                </a>
                <a href="settings.php" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>

                <div class="nav-divider"></div>

                <a href="../index.php" class="nav-item nav-secondary">
                    <i class="fas fa-home"></i>
                    <span>Back to Site</span>
                </a>
                <a href="../logout.php" class="nav-item nav-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>
            
            <div class="sidebar-footer">
                <a href="logout.php" class="nav-item logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="content-header">
                <div class="header-left">
                    <h1>AI Questions Management</h1>
                    <p>Generate and manage AI-powered questions for all subjects</p>
                </div>
                <div class="header-right">
                    <div class="header-stats">
                        <div class="stat-item">
                            <span class="stat-number"><?php echo $totalQuestions; ?></span>
                            <span class="stat-label">Total Questions</span>
                        </div>
                        <?php foreach ($questionsByDifficulty as $stat): ?>
                            <div class="stat-item">
                                <span class="stat-number"><?php echo $stat['count']; ?></span>
                                <span class="stat-label"><?php echo ucfirst($stat['difficulty']); ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <button class="btn btn-primary" onclick="openGenerateModal()">
                        <i class="fas fa-robot"></i>
                        Generate Questions
                    </button>
                </div>
            </header>
            
            <!-- Messages -->
            <?php if (isset($success)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <!-- AI Generation Notice -->
            <div class="ai-notice">
                <div class="notice-content">
                    <i class="fas fa-robot"></i>
                    <div>
                        <h3>AI-Powered Question Generation</h3>
                        <p>Questions are automatically generated using advanced AI technology. The system creates subject-specific, level-appropriate questions with varying difficulty levels.</p>
                    </div>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="filters-section">
                <form method="GET" class="filters-form">
                    <div class="filter-controls">
                        <select name="subject">
                            <option value="">All Subjects</option>
                            <?php foreach ($subjects as $subject): ?>
                                <option value="<?php echo $subject['id']; ?>" <?php echo $subject_filter == $subject['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($subject['name'] . ' (' . $subject['department_name'] . ')'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        
                        <select name="difficulty">
                            <option value="">All Difficulties</option>
                            <option value="easy" <?php echo $difficulty_filter === 'easy' ? 'selected' : ''; ?>>Easy</option>
                            <option value="medium" <?php echo $difficulty_filter === 'medium' ? 'selected' : ''; ?>>Medium</option>
                            <option value="hard" <?php echo $difficulty_filter === 'hard' ? 'selected' : ''; ?>>Hard</option>
                        </select>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i>
                            Filter
                        </button>
                        
                        <a href="questions.php" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            Clear
                        </a>
                    </div>
                </form>
            </div>
            
            <!-- Questions List -->
            <div class="questions-container">
                <?php if (empty($questions)): ?>
                    <div class="empty-state">
                        <i class="fas fa-robot"></i>
                        <h3>No Questions Found</h3>
                        <p>Start by generating AI questions for your departments and levels</p>
                        <button class="btn btn-primary" onclick="openGenerateModal()">
                            <i class="fas fa-robot"></i>
                            Generate First Questions
                        </button>
                    </div>
                <?php else: ?>
                    <div class="questions-list">
                        <?php foreach ($questions as $question): ?>
                            <div class="question-card">
                                <div class="question-header">
                                    <div class="question-meta">
                                        <span class="department-tag"><?php echo htmlspecialchars($question['department_name'] . ' - ' . $question['level_name']); ?></span>
                                        <span class="difficulty-badge <?php echo $question['difficulty']; ?>">
                                            <?php echo ucfirst($question['difficulty']); ?>
                                        </span>
                                    </div>
                                    <div class="question-actions">
                                        <button class="btn-icon" onclick="viewQuestion(<?php echo $question['id']; ?>)" title="View Question">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn-icon delete" onclick="deleteQuestion(<?php echo $question['id']; ?>)" title="Delete Question">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="question-content">
                                    <p class="question-text"><?php echo htmlspecialchars($question['question_text']); ?></p>
                                    
                                    <div class="question-options">
                                        <div class="option <?php echo $question['correct_answer'] === 'A' ? 'correct' : ''; ?>">
                                            <span class="option-letter">A</span>
                                            <span><?php echo htmlspecialchars($question['option_a']); ?></span>
                                        </div>
                                        <div class="option <?php echo $question['correct_answer'] === 'B' ? 'correct' : ''; ?>">
                                            <span class="option-letter">B</span>
                                            <span><?php echo htmlspecialchars($question['option_b']); ?></span>
                                        </div>
                                        <div class="option <?php echo $question['correct_answer'] === 'C' ? 'correct' : ''; ?>">
                                            <span class="option-letter">C</span>
                                            <span><?php echo htmlspecialchars($question['option_c']); ?></span>
                                        </div>
                                        <div class="option <?php echo $question['correct_answer'] === 'D' ? 'correct' : ''; ?>">
                                            <span class="option-letter">D</span>
                                            <span><?php echo htmlspecialchars($question['option_d']); ?></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="question-footer">
                                    <small>Generated: <?php echo date('M d, Y H:i', strtotime($question['created_at'])); ?></small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
    
    <!-- Generate Questions Modal -->
    <div id="generateModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Generate AI Questions</h3>
                <span class="close" onclick="closeGenerateModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="generateForm" method="POST">
                    <input type="hidden" name="action" value="generate_questions">
                    
                    <div class="form-group">
                        <label for="subject_id">Subject *</label>
                        <select id="subject_id" name="subject_id" required>
                            <option value="">Select Subject</option>
                            <?php foreach ($subjects as $subject): ?>
                                <option value="<?php echo $subject['id']; ?>">
                                    <?php echo htmlspecialchars($subject['name'] . ' (' . $subject['department_name'] . ' - ' . $subject['level_name'] . ')'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="difficulty">Difficulty *</label>
                            <select id="difficulty" name="difficulty" required>
                                <option value="">Select Difficulty</option>
                                <option value="easy">Easy</option>
                                <option value="medium">Medium</option>
                                <option value="hard">Hard</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="count">Number of Questions *</label>
                            <input type="number" id="count" name="count" min="1" max="100" value="20" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="topics">Specific Topics (Optional)</label>
                        <textarea id="topics" name="topics" rows="3" placeholder="Enter specific topics or areas to focus on (optional)"></textarea>
                    </div>
                    
                    <div class="ai-info">
                        <i class="fas fa-info-circle"></i>
                        <p>The AI will generate questions based on the subject curriculum, difficulty level, and any specific topics you mention. Questions will be automatically saved to the database.</p>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeGenerateModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-robot"></i>
                            Generate Questions
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="../assets/js/admin-dashboard.js"></script>
    <script src="../assets/js/questions.js"></script>
</body>
</html>
