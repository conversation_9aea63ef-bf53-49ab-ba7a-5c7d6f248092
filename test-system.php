<?php
/**
 * Test System Functionality
 * Quick test to verify student management and AI question generation
 */

require_once 'config/database.php';

echo "<h1>LMS System Test</h1>";

// Test 1: Check database connection
echo "<h2>1. Database Connection Test</h2>";
try {
    $conn = getConnection();
    echo "<p style='color: green;'>✓ Database connection successful</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
    exit;
}

// Test 2: Check if sample data exists
echo "<h2>2. Sample Data Check</h2>";

$departments = fetchAll("SELECT * FROM departments LIMIT 3");
echo "<p>Departments: " . count($departments) . " found</p>";

$levels = fetchAll("SELECT * FROM academic_levels LIMIT 5");
echo "<p>Academic Levels: " . count($levels) . " found</p>";

$subjects = fetchAll("SELECT * FROM subjects LIMIT 5");
echo "<p>Subjects: " . count($subjects) . " found</p>";

$students = fetchAll("SELECT * FROM students LIMIT 5");
echo "<p>Students: " . count($students) . " found</p>";

$questions = fetchAll("SELECT * FROM questions LIMIT 5");
echo "<p>Questions: " . count($questions) . " found</p>";

// Test 3: Create a test student if none exist
echo "<h2>3. Test Student Creation</h2>";

if (count($students) == 0 && count($departments) > 0 && count($levels) > 0) {
    echo "<p>No students found. Creating test student...</p>";
    
    try {
        $testStudentId = insertRecord('students', [
            'student_id' => 'TEST001',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '08012345678',
            'password_hash' => password_hash('password123', PASSWORD_DEFAULT),
            'department_id' => $departments[0]['id'],
            'academic_level_id' => $levels[0]['id'],
            'is_approved' => 0,
            'total_points' => 150
        ]);
        
        if ($testStudentId) {
            echo "<p style='color: green;'>✓ Test student created successfully (ID: $testStudentId)</p>";
            
            // Create some test quiz sessions for the student
            for ($i = 1; $i <= 3; $i++) {
                insertRecord('quiz_sessions', [
                    'student_id' => $testStudentId,
                    'subject_id' => !empty($subjects) ? $subjects[0]['id'] : null,
                    'score' => rand(6, 10),
                    'total_questions' => 10,
                    'time_taken' => rand(300, 600),
                    'created_at' => date('Y-m-d H:i:s', strtotime("-$i days"))
                ]);
            }
            echo "<p style='color: green;'>✓ Test quiz sessions created</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Failed to create test student: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>Students already exist or missing required data</p>";
}

// Test 4: Test AJAX endpoints
echo "<h2>4. AJAX Endpoints Test</h2>";

$ajaxFiles = [
    'admin/ajax/student-progress.php',
    'admin/ajax/refresh-student-stats.php',
    'admin/generate-questions.php'
];

foreach ($ajaxFiles as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✓ $file exists</p>";
    } else {
        echo "<p style='color: red;'>✗ $file missing</p>";
    }
}

// Test 5: AI Question Generation Test
echo "<h2>5. AI Question Generation Test</h2>";

if (count($departments) > 0) {
    echo "<p>Testing AI question generation for department: " . $departments[0]['name'] . "</p>";
    echo "<p style='color: blue;'>→ <a href='admin/generate-questions.php' target='_blank'>Open AI Question Generator</a></p>";
} else {
    echo "<p style='color: orange;'>⚠ No departments found for AI testing</p>";
}

// Test 6: Student Management Test
echo "<h2>6. Student Management Test</h2>";

$updatedStudents = fetchAll("SELECT * FROM students LIMIT 5");
if (count($updatedStudents) > 0) {
    echo "<p style='color: green;'>✓ " . count($updatedStudents) . " students available for testing</p>";
    echo "<p style='color: blue;'>→ <a href='admin/students.php' target='_blank'>Open Student Management</a></p>";
    
    foreach ($updatedStudents as $student) {
        echo "<div style='margin: 10px; padding: 10px; border: 1px solid #ddd;'>";
        echo "<strong>{$student['first_name']} {$student['last_name']}</strong> ({$student['student_id']})<br>";
        echo "Status: " . ($student['is_approved'] ? 'Approved' : 'Pending') . "<br>";
        echo "Points: {$student['total_points']}<br>";
        echo "<button onclick=\"testProgress({$student['id']})\" style='margin: 5px; padding: 5px 10px;'>Test Progress</button>";
        echo "<button onclick=\"testApprove({$student['id']})\" style='margin: 5px; padding: 5px 10px;'>Test Approve</button>";
        echo "</div>";
    }
} else {
    echo "<p style='color: orange;'>⚠ No students found for testing</p>";
}

echo "<h2>7. System Status Summary</h2>";
echo "<ul>";
echo "<li>Database: Connected ✓</li>";
echo "<li>Departments: " . count($departments) . "</li>";
echo "<li>Students: " . count($updatedStudents) . "</li>";
echo "<li>Questions: " . count($questions) . "</li>";
echo "<li>AI Generation: Available ✓</li>";
echo "</ul>";

?>

<script>
function testProgress(studentId) {
    fetch('admin/ajax/student-progress.php?id=' + studentId)
        .then(response => response.text())
        .then(data => {
            const popup = window.open('', 'progress', 'width=800,height=600');
            popup.document.write('<html><head><title>Student Progress</title></head><body>' + data + '</body></html>');
        })
        .catch(error => {
            alert('Error testing progress: ' + error);
        });
}

function testApprove(studentId) {
    if (confirm('Test approve student?')) {
        fetch('admin/students.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=approve&student_id=' + studentId
        })
        .then(response => response.text())
        .then(data => {
            alert('Approve test completed');
            location.reload();
        })
        .catch(error => {
            alert('Error testing approve: ' + error);
        });
    }
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
p { margin: 5px 0; }
</style>
