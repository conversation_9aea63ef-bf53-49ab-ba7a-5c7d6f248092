<?php
/**
 * Check Database Structure for Quiz System
 */

require_once '../config/database.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Check Database Structure</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; padding: 10px; background: #d4edda; border-radius: 5px; margin: 10px 0; }
        .error { color: red; padding: 10px; background: #f8d7da; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; padding: 10px; background: #d1ecf1; border-radius: 5px; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>";

echo "<h1>🔍 Database Structure Check</h1>";

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check tables
    echo "<div class='section'>";
    echo "<h2>📋 Available Tables</h2>";
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredTables = ['students', 'departments', 'academic_levels', 'questions', 'quiz_sessions', 'quiz_questions', 'quiz_answers'];
    
    echo "<table>";
    echo "<tr><th>Table Name</th><th>Status</th></tr>";
    foreach ($requiredTables as $table) {
        $exists = in_array($table, $tables);
        $status = $exists ? "<span style='color: green;'>✅ Exists</span>" : "<span style='color: red;'>❌ Missing</span>";
        echo "<tr><td>$table</td><td>$status</td></tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // Check students table structure
    if (in_array('students', $tables)) {
        echo "<div class='section'>";
        echo "<h2>👨‍🎓 Students Table Structure</h2>";
        $columns = $pdo->query("DESCRIBE students")->fetchAll();
        echo "<table>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>{$col['Field']}</td>";
            echo "<td>{$col['Type']}</td>";
            echo "<td>{$col['Null']}</td>";
            echo "<td>{$col['Key']}</td>";
            echo "<td>{$col['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check if there are students
        $studentCount = $pdo->query("SELECT COUNT(*) FROM students")->fetchColumn();
        echo "<div class='info'>📊 Total students: $studentCount</div>";
        
        if ($studentCount > 0) {
            $students = $pdo->query("SELECT student_id, first_name, last_name, department_id, academic_level_id FROM students LIMIT 5")->fetchAll();
            echo "<h3>Sample Students:</h3>";
            echo "<table>";
            echo "<tr><th>Student ID</th><th>Name</th><th>Department ID</th><th>Level ID</th></tr>";
            foreach ($students as $student) {
                echo "<tr>";
                echo "<td>{$student['student_id']}</td>";
                echo "<td>{$student['first_name']} {$student['last_name']}</td>";
                echo "<td>{$student['department_id']}</td>";
                echo "<td>{$student['academic_level_id']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        echo "</div>";
    }
    
    // Check departments
    if (in_array('departments', $tables)) {
        echo "<div class='section'>";
        echo "<h2>🏫 Departments Table</h2>";
        $deptCount = $pdo->query("SELECT COUNT(*) FROM departments")->fetchColumn();
        echo "<div class='info'>📊 Total departments: $deptCount</div>";
        
        if ($deptCount > 0) {
            $departments = $pdo->query("SELECT id, name, code FROM departments LIMIT 10")->fetchAll();
            echo "<table>";
            echo "<tr><th>ID</th><th>Name</th><th>Code</th></tr>";
            foreach ($departments as $dept) {
                echo "<tr><td>{$dept['id']}</td><td>{$dept['name']}</td><td>{$dept['code']}</td></tr>";
            }
            echo "</table>";
        }
        echo "</div>";
    }
    
    // Check academic levels
    if (in_array('academic_levels', $tables)) {
        echo "<div class='section'>";
        echo "<h2>🎓 Academic Levels Table</h2>";
        $levelCount = $pdo->query("SELECT COUNT(*) FROM academic_levels")->fetchColumn();
        echo "<div class='info'>📊 Total academic levels: $levelCount</div>";
        
        if ($levelCount > 0) {
            $levels = $pdo->query("SELECT id, level_name, level_code FROM academic_levels")->fetchAll();
            echo "<table>";
            echo "<tr><th>ID</th><th>Level Name</th><th>Code</th></tr>";
            foreach ($levels as $level) {
                echo "<tr><td>{$level['id']}</td><td>{$level['level_name']}</td><td>{$level['level_code']}</td></tr>";
            }
            echo "</table>";
        }
        echo "</div>";
    }
    
    // Check questions table
    if (in_array('questions', $tables)) {
        echo "<div class='section'>";
        echo "<h2>❓ Questions Table Structure</h2>";
        $columns = $pdo->query("DESCRIBE questions")->fetchAll();
        echo "<table>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>{$col['Field']}</td>";
            echo "<td>{$col['Type']}</td>";
            echo "<td>{$col['Null']}</td>";
            echo "<td>{$col['Key']}</td>";
            echo "<td>{$col['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        $questionCount = $pdo->query("SELECT COUNT(*) FROM questions")->fetchColumn();
        echo "<div class='info'>📊 Total questions: $questionCount</div>";
        echo "</div>";
    }
    
    // Check quiz_sessions table
    if (in_array('quiz_sessions', $tables)) {
        echo "<div class='section'>";
        echo "<h2>📝 Quiz Sessions Table Structure</h2>";
        $columns = $pdo->query("DESCRIBE quiz_sessions")->fetchAll();
        echo "<table>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>{$col['Field']}</td>";
            echo "<td>{$col['Type']}</td>";
            echo "<td>{$col['Null']}</td>";
            echo "<td>{$col['Key']}</td>";
            echo "<td>{$col['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        $sessionCount = $pdo->query("SELECT COUNT(*) FROM quiz_sessions")->fetchColumn();
        echo "<div class='info'>📊 Total quiz sessions: $sessionCount</div>";
        echo "</div>";
    }
    
    echo "<div class='success'>✅ Database structure check completed!</div>";
    echo "<p><a href='test-quiz-generation.php'>🧪 Test Quiz Generation</a></p>";
    echo "<p><a href='create-test-student.php'>👨‍🎓 Create Test Student</a></p>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
}

echo "</body></html>";
?>
