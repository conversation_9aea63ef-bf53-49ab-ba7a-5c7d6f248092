<?php
/**
 * Database Check Script
 */

// Include database configuration
require_once __DIR__ . '/../config/database.php';

echo "<h2>Database Connection Test</h2>";

try {
    // Test basic connection
    $pdo = getConnection();
    echo "<p>✅ Database connection successful</p>";
    
    // Check if questions table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'questions'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "<p>✅ Questions table exists</p>";
        
        // Try to count questions
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM questions");
            $result = $stmt->fetch();
            echo "<p>✅ Questions count: " . $result['count'] . "</p>";
        } catch (Exception $e) {
            echo "<p>❌ Error counting questions: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>❌ Questions table does not exist</p>";
        echo "<p>Creating questions table...</p>";
        
        // Create questions table
        $createTableSQL = "
        CREATE TABLE questions (
            id INT PRIMARY KEY AUTO_INCREMENT,
            department_id INT NOT NULL,
            subject_id INT NOT NULL,
            question_text TEXT NOT NULL,
            option_a VARCHAR(255) NOT NULL,
            option_b VARCHAR(255) NOT NULL,
            option_c VARCHAR(255) NOT NULL,
            option_d VARCHAR(255) NOT NULL,
            correct_answer ENUM('A', 'B', 'C', 'D') NOT NULL,
            explanation TEXT,
            difficulty ENUM('easy', 'medium', 'hard') DEFAULT 'medium',
            topic VARCHAR(100),
            source VARCHAR(50) DEFAULT 'Manual',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (department_id) REFERENCES departments(id),
            FOREIGN KEY (subject_id) REFERENCES subjects(id)
        )";
        
        try {
            $pdo->exec($createTableSQL);
            echo "<p>✅ Questions table created successfully</p>";
        } catch (Exception $e) {
            echo "<p>❌ Error creating questions table: " . $e->getMessage() . "</p>";
        }
    }
    
    // Check other required tables
    $requiredTables = ['departments', 'subjects', 'students'];
    foreach ($requiredTables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        $exists = $stmt->rowCount() > 0;
        echo "<p>" . ($exists ? "✅" : "❌") . " Table '$table' " . ($exists ? "exists" : "missing") . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Database connection failed: " . $e->getMessage() . "</p>";
}

echo "<br><a href='generate-questions.php'>← Back to Generate Questions</a>";
?>
