<?php
/**
 * Departments Management for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Require admin login
requireLogin('admin');

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add_department':
                    $name = trim($_POST['name']);
                    $code = trim($_POST['code']);
                    $description = trim($_POST['description']);
                    
                    if (empty($name) || empty($code)) {
                        throw new Exception('Department name and code are required');
                    }
                    
                    // Check if department code already exists
                    $existing = fetchOne("SELECT id FROM departments WHERE code = :code", ['code' => $code]);
                    if ($existing) {
                        throw new Exception('Department code already exists');
                    }
                    
                    execute("
                        INSERT INTO departments (name, code, description, created_at) 
                        VALUES (:name, :code, :description, NOW())
                    ", [
                        'name' => $name,
                        'code' => $code,
                        'description' => $description
                    ]);
                    
                    $success = "Department added successfully!";
                    break;
                    
                case 'edit_department':
                    $id = (int)$_POST['id'];
                    $name = trim($_POST['name']);
                    $code = trim($_POST['code']);
                    $description = trim($_POST['description']);
                    
                    if (empty($name) || empty($code)) {
                        throw new Exception('Department name and code are required');
                    }
                    
                    // Check if department code already exists (excluding current department)
                    $existing = fetchOne("SELECT id FROM departments WHERE code = :code AND id != :id", ['code' => $code, 'id' => $id]);
                    if ($existing) {
                        throw new Exception('Department code already exists');
                    }
                    
                    execute("
                        UPDATE departments 
                        SET name = :name, code = :code, description = :description, updated_at = NOW()
                        WHERE id = :id
                    ", [
                        'id' => $id,
                        'name' => $name,
                        'code' => $code,
                        'description' => $description
                    ]);
                    
                    $success = "Department updated successfully!";
                    break;
                    
                case 'delete_department':
                    $id = (int)$_POST['id'];
                    
                    // Check if department has students
                    $studentCount = fetchOne("SELECT COUNT(*) as count FROM students WHERE department_id = :id", ['id' => $id])['count'];
                    if ($studentCount > 0) {
                        throw new Exception("Cannot delete department with existing students. Please transfer students first.");
                    }
                    
                    // Check if department has subjects
                    $subjectCount = fetchOne("SELECT COUNT(*) as count FROM subjects WHERE department_id = :id", ['id' => $id])['count'];
                    if ($subjectCount > 0) {
                        throw new Exception("Cannot delete department with existing subjects. Please remove subjects first.");
                    }
                    
                    execute("DELETE FROM departments WHERE id = :id", ['id' => $id]);
                    $success = "Department deleted successfully!";
                    break;
            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get departments with statistics
$departments = fetchAll("
    SELECT d.*,
           (SELECT COUNT(*) FROM students WHERE department_id = d.id) as student_count,
           (SELECT COUNT(*) FROM subjects WHERE department_id = d.id) as subject_count,
           (SELECT COUNT(*) FROM students WHERE department_id = d.id AND is_approved = 1) as approved_students
    FROM departments d
    ORDER BY d.name
");

// Ensure $departments is always an array
if (!$departments) {
    $departments = [];
}

$pageTitle = 'Departments Management';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Admin Panel</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="students.php" class="nav-item">
                    <i class="fas fa-users"></i>
                    <span>Students</span>
                </a>
                <a href="departments.php" class="nav-item active">
                    <i class="fas fa-building"></i>
                    <span>Departments</span>
                </a>
                <a href="subjects.php" class="nav-item">
                    <i class="fas fa-book"></i>
                    <span>Subjects</span>
                </a>
                <a href="questions.php" class="nav-item">
                    <i class="fas fa-robot"></i>
                    <span>AI Questions</span>
                </a>
                <a href="rewards.php" class="nav-item">
                    <i class="fas fa-trophy"></i>
                    <span>Rewards</span>
                </a>
                <a href="analytics.php" class="nav-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>Analytics</span>
                </a>
                <a href="settings.php" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>

                <div class="nav-divider"></div>

                <a href="../index.php" class="nav-item nav-secondary">
                    <i class="fas fa-home"></i>
                    <span>Back to Site</span>
                </a>
                <a href="../logout.php" class="nav-item nav-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>
            
            <div class="sidebar-footer">
                <a href="logout.php" class="nav-item logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="content-header">
                <div class="header-left">
                    <h1>Departments Management</h1>
                    <p>Manage academic departments and their configurations</p>
                </div>
                <div class="header-right">
                    <button class="btn btn-primary" onclick="openAddDepartmentModal()">
                        <i class="fas fa-plus"></i>
                        Add New Department
                    </button>
                </div>
            </header>
            
            <!-- Messages -->
            <?php if (isset($success)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <!-- Departments Grid -->
            <div class="departments-container">
                <?php if (empty($departments)): ?>
                    <div class="empty-state">
                        <i class="fas fa-building"></i>
                        <h3>No Departments Found</h3>
                        <p>Start by adding your first academic department</p>
                        <button class="btn btn-primary" onclick="openAddDepartmentModal()">
                            <i class="fas fa-plus"></i>
                            Add First Department
                        </button>
                    </div>
                <?php else: ?>
                    <div class="departments-grid">
                        <?php foreach ($departments as $department): ?>
                            <div class="department-card">
                                <div class="department-header">
                                    <div class="department-icon">
                                        <i class="fas fa-building"></i>
                                    </div>
                                    <div class="department-actions">
                                        <button class="btn-icon" onclick="editDepartment(<?php echo $department['id']; ?>)" title="Edit Department">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon delete" onclick="deleteDepartment(<?php echo $department['id']; ?>, '<?php echo htmlspecialchars($department['name']); ?>')" title="Delete Department">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="department-content">
                                    <h3><?php echo htmlspecialchars($department['name']); ?></h3>
                                    <p class="department-code"><?php echo htmlspecialchars($department['code']); ?></p>
                                    
                                    <?php if ($department['description']): ?>
                                        <p class="department-description"><?php echo htmlspecialchars($department['description']); ?></p>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="department-stats">
                                    <div class="stat-item">
                                        <i class="fas fa-users"></i>
                                        <span><?php echo $department['student_count']; ?> students</span>
                                    </div>
                                    <div class="stat-item">
                                        <i class="fas fa-book"></i>
                                        <span><?php echo $department['subject_count']; ?> subjects</span>
                                    </div>
                                    <div class="stat-item">
                                        <i class="fas fa-check-circle"></i>
                                        <span><?php echo $department['approved_students']; ?> approved</span>
                                    </div>
                                </div>
                                
                                <div class="department-footer">
                                    <small>Created: <?php echo date('M d, Y', strtotime($department['created_at'])); ?></small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
    
    <!-- Add/Edit Department Modal -->
    <div id="departmentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Add New Department</h3>
                <span class="close" onclick="closeDepartmentModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="departmentForm" method="POST">
                    <input type="hidden" name="action" id="formAction" value="add_department">
                    <input type="hidden" name="id" id="departmentId">
                    
                    <div class="form-group">
                        <label for="name">Department Name *</label>
                        <input type="text" id="name" name="name" required placeholder="e.g., Computer Science">
                    </div>
                    
                    <div class="form-group">
                        <label for="code">Department Code *</label>
                        <input type="text" id="code" name="code" required placeholder="e.g., CSC" maxlength="10">
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" rows="4" placeholder="Optional department description"></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeDepartmentModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            <span id="submitText">Add Department</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="../assets/js/admin-dashboard.js"></script>
    <script src="../assets/js/departments.js"></script>
</body>
</html>
