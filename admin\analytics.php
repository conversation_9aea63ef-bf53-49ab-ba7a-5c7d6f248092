<?php
/**
 * Analytics Dashboard for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Require admin login
requireLogin('admin');

// Get date range from query parameters
$startDate = $_GET['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
$endDate = $_GET['end_date'] ?? date('Y-m-d');

// Validate dates
if (!strtotime($startDate) || !strtotime($endDate)) {
    $startDate = date('Y-m-d', strtotime('-30 days'));
    $endDate = date('Y-m-d');
}

// Get overall statistics
$totalStudents = fetchOne("SELECT COUNT(*) as count FROM students WHERE is_approved = 1")['count'];
$totalQuizzes = fetchOne("SELECT COUNT(*) as count FROM quiz_sessions WHERE created_at BETWEEN :start AND :end", [
    'start' => $startDate . ' 00:00:00',
    'end' => $endDate . ' 23:59:59'
])['count'];

$avgScoreResult = fetchOne("
    SELECT AVG(score/total_questions*100) as avg_score
    FROM quiz_sessions
    WHERE status = 'completed' AND created_at BETWEEN :start AND :end
", [
    'start' => $startDate . ' 00:00:00',
    'end' => $endDate . ' 23:59:59'
]);
$avgScore = $avgScoreResult ? $avgScoreResult['avg_score'] : 0;

$completionRateResult = fetchOne("
    SELECT
        (COUNT(CASE WHEN status = 'completed' THEN 1 END) / COUNT(*) * 100) as completion_rate
    FROM quiz_sessions
    WHERE created_at BETWEEN :start AND :end
", [
    'start' => $startDate . ' 00:00:00',
    'end' => $endDate . ' 23:59:59'
]);
$completionRate = $completionRateResult ? $completionRateResult['completion_rate'] : 0;

// Get daily quiz activity
$dailyActivity = fetchAll("
    SELECT 
        DATE(created_at) as date,
        COUNT(*) as quiz_count,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
        AVG(CASE WHEN status = 'completed' THEN score/total_questions*100 END) as avg_score
    FROM quiz_sessions
    WHERE created_at BETWEEN :start AND :end
    GROUP BY DATE(created_at)
    ORDER BY date
", [
    'start' => $startDate . ' 00:00:00',
    'end' => $endDate . ' 23:59:59'
]);

// Get department performance
$departmentStats = fetchAll("
    SELECT 
        d.name as department_name,
        COUNT(DISTINCT s.id) as student_count,
        COUNT(qs.id) as quiz_count,
        COUNT(CASE WHEN qs.status = 'completed' THEN 1 END) as completed_quizzes,
        AVG(CASE WHEN qs.status = 'completed' THEN qs.score/qs.total_questions*100 END) as avg_score,
        (COUNT(CASE WHEN qs.status = 'completed' THEN 1 END) / COUNT(qs.id) * 100) as completion_rate
    FROM departments d
    LEFT JOIN students s ON d.id = s.department_id AND s.is_approved = 1
    LEFT JOIN quiz_sessions qs ON s.id = qs.student_id 
        AND qs.created_at BETWEEN :start AND :end
    GROUP BY d.id, d.name
    HAVING quiz_count > 0
    ORDER BY avg_score DESC
", [
    'start' => $startDate . ' 00:00:00',
    'end' => $endDate . ' 23:59:59'
]);

// Get top performing students
$topStudents = fetchAll("
    SELECT 
        s.first_name, s.last_name, s.student_id,
        d.name as department_name,
        COUNT(qs.id) as quiz_count,
        AVG(qs.score/qs.total_questions*100) as avg_score,
        s.total_points
    FROM students s
    JOIN departments d ON s.department_id = d.id
    JOIN quiz_sessions qs ON s.id = qs.student_id
    WHERE qs.status = 'completed' 
        AND qs.created_at BETWEEN :start AND :end
    GROUP BY s.id, s.first_name, s.last_name, s.student_id, d.name, s.total_points
    HAVING quiz_count >= 3
    ORDER BY avg_score DESC, quiz_count DESC
    LIMIT 10
", [
    'start' => $startDate . ' 00:00:00',
    'end' => $endDate . ' 23:59:59'
]);

$pageTitle = 'Analytics Dashboard';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Admin Panel</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="students.php" class="nav-item">
                    <i class="fas fa-users"></i>
                    <span>Students</span>
                </a>
                <a href="departments.php" class="nav-item">
                    <i class="fas fa-building"></i>
                    <span>Departments</span>
                </a>
                <a href="questions.php" class="nav-item">
                    <i class="fas fa-robot"></i>
                    <span>AI Questions</span>
                </a>
                <a href="rewards.php" class="nav-item">
                    <i class="fas fa-trophy"></i>
                    <span>Rewards</span>
                </a>
                <a href="analytics.php" class="nav-item active">
                    <i class="fas fa-chart-bar"></i>
                    <span>Analytics</span>
                </a>
                <a href="settings.php" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>

                <div class="nav-divider"></div>

                <a href="../index.php" class="nav-item nav-secondary">
                    <i class="fas fa-home"></i>
                    <span>Back to Site</span>
                </a>
                <a href="../logout.php" class="nav-item nav-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>
            
            <div class="sidebar-footer">
                <a href="logout.php" class="nav-item logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="content-header">
                <div class="header-left">
                    <h1>Analytics Dashboard</h1>
                    <p>Comprehensive insights into system performance and student progress</p>
                </div>
                <div class="header-right">
                    <form method="GET" class="date-filter">
                        <input type="date" name="start_date" value="<?php echo $startDate; ?>" max="<?php echo date('Y-m-d'); ?>">
                        <span>to</span>
                        <input type="date" name="end_date" value="<?php echo $endDate; ?>" max="<?php echo date('Y-m-d'); ?>">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i>
                            Filter
                        </button>
                    </form>
                </div>
            </header>
            
            <!-- Key Metrics -->
            <div class="analytics-metrics">
                <div class="metric-card">
                    <div class="metric-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="metric-content">
                        <h3><?php echo number_format($totalStudents); ?></h3>
                        <p>Active Students</p>
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-icon">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                    <div class="metric-content">
                        <h3><?php echo number_format($totalQuizzes); ?></h3>
                        <p>Quizzes Taken</p>
                        <small><?php echo $startDate; ?> to <?php echo $endDate; ?></small>
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="metric-content">
                        <h3><?php echo round($avgScore ?? 0, 1); ?>%</h3>
                        <p>Average Score</p>
                        <small>Completed quizzes only</small>
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="metric-content">
                        <h3><?php echo round($completionRate ?? 0, 1); ?>%</h3>
                        <p>Completion Rate</p>
                        <small>Started vs completed</small>
                    </div>
                </div>
            </div>
            
            <!-- Charts Section -->
            <div class="analytics-charts">
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>Daily Quiz Activity</h3>
                    </div>
                    <canvas id="dailyActivityChart"></canvas>
                </div>
                
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>Department Performance</h3>
                    </div>
                    <canvas id="departmentChart"></canvas>
                </div>
            </div>
            
            <!-- Performance Tables -->
            <div class="analytics-tables">
                <!-- Top Students -->
                <div class="table-container">
                    <div class="table-header">
                        <h3>Top Performing Students</h3>
                    </div>
                    <table class="analytics-table">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Student</th>
                                <th>Department</th>
                                <th>Quizzes</th>
                                <th>Avg Score</th>
                                <th>Points</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($topStudents as $index => $student): ?>
                                <tr>
                                    <td>
                                        <?php if ($index === 0): ?>
                                            <i class="fas fa-crown gold"></i>
                                        <?php elseif ($index === 1): ?>
                                            <i class="fas fa-medal silver"></i>
                                        <?php elseif ($index === 2): ?>
                                            <i class="fas fa-medal bronze"></i>
                                        <?php else: ?>
                                            <span class="rank"><?php echo $index + 1; ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></strong>
                                        <small><?php echo htmlspecialchars($student['student_id']); ?></small>
                                    </td>
                                    <td><?php echo htmlspecialchars($student['department_name']); ?></td>
                                    <td><?php echo $student['quiz_count']; ?></td>
                                    <td>
                                        <span class="score-badge <?php echo $student['avg_score'] >= 80 ? 'excellent' : ($student['avg_score'] >= 70 ? 'good' : ($student['avg_score'] >= 60 ? 'average' : 'poor')); ?>">
                                            <?php echo round($student['avg_score'], 1); ?>%
                                        </span>
                                    </td>
                                    <td><?php echo number_format($student['total_points']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Department Stats -->
                <div class="table-container">
                    <div class="table-header">
                        <h3>Department Performance</h3>
                    </div>
                    <table class="analytics-table">
                        <thead>
                            <tr>
                                <th>Department</th>
                                <th>Students</th>
                                <th>Quizzes</th>
                                <th>Completion Rate</th>
                                <th>Avg Score</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($departmentStats as $dept): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($dept['department_name']); ?></strong></td>
                                    <td><?php echo $dept['student_count']; ?></td>
                                    <td><?php echo $dept['quiz_count']; ?></td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: <?php echo round($dept['completion_rate'] ?? 0); ?>%"></div>
                                            <span><?php echo round($dept['completion_rate'] ?? 0, 1); ?>%</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="score-badge <?php echo ($dept['avg_score'] ?? 0) >= 70 ? 'good' : (($dept['avg_score'] ?? 0) >= 50 ? 'average' : 'poor'); ?>">
                                            <?php echo round($dept['avg_score'] ?? 0, 1); ?>%
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>
    
    <script src="../assets/js/admin-dashboard.js"></script>
    <script>
        // Daily Activity Chart
        const dailyCtx = document.getElementById('dailyActivityChart').getContext('2d');
        const dailyData = <?php echo json_encode($dailyActivity); ?>;
        
        new Chart(dailyCtx, {
            type: 'line',
            data: {
                labels: dailyData.map(d => d.date),
                datasets: [{
                    label: 'Quizzes Started',
                    data: dailyData.map(d => d.quiz_count),
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Quizzes Completed',
                    data: dailyData.map(d => d.completed_count),
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // Department Performance Chart
        const deptCtx = document.getElementById('departmentChart').getContext('2d');
        const deptData = <?php echo json_encode($departmentStats); ?>;
        
        new Chart(deptCtx, {
            type: 'bar',
            data: {
                labels: deptData.map(d => d.department_name),
                datasets: [{
                    label: 'Average Score (%)',
                    data: deptData.map(d => Math.round(d.avg_score || 0)),
                    backgroundColor: [
                        '#3b82f6', '#10b981', '#f59e0b', '#ef4444', 
                        '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    </script>
</body>
</html>
