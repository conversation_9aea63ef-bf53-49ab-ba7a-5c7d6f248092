<?php
/**
 * Detailed Quiz Result for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Check if student is logged in
startSecureSession();
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'student') {
    header('Location: login.php');
    exit();
}

$quizId = $_GET['id'] ?? null;
if (!$quizId || !is_numeric($quizId)) {
    header('Location: results.php');
    exit();
}

// Get student information
$student = fetchOne("
    SELECT s.*, d.name as department_name, al.level_name
    FROM students s
    JOIN departments d ON s.department_id = d.id
    JOIN academic_levels al ON s.academic_level_id = al.id
    WHERE s.id = :id
", ['id' => $_SESSION['user_id']]);

// Get quiz session details
$quiz = fetchOne("
    SELECT qs.*, d.name as department_name, al.level_name,
           CASE 
               WHEN qs.score >= 90 THEN 'Excellent'
               WHEN qs.score >= 80 THEN 'Very Good'
               WHEN qs.score >= 70 THEN 'Good'
               WHEN qs.score >= 60 THEN 'Fair'
               ELSE 'Poor'
           END as grade
    FROM quiz_sessions qs
    JOIN departments d ON qs.department_id = d.id
    JOIN academic_levels al ON qs.academic_level_id = al.id
    WHERE qs.id = :id AND qs.student_id = :student_id AND qs.status = 'completed'
", ['id' => $quizId, 'student_id' => $student['id']]);

if (!$quiz) {
    header('Location: results.php');
    exit();
}

// Get quiz answers with questions
$answers = fetchAll("
    SELECT qa.*, q.question_text, q.option_a, q.option_b, q.option_c, q.option_d, 
           q.correct_answer, q.explanation, q.difficulty
    FROM quiz_answers qa
    JOIN questions q ON qa.question_id = q.id
    WHERE qa.quiz_session_id = :quiz_id
    ORDER BY qa.question_number
", ['quiz_id' => $quizId]);

$pageTitle = 'Quiz Result Details';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .result-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        .result-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        .score-display {
            font-size: 48px;
            font-weight: 800;
            margin: 20px 0;
        }
        .grade-badge {
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            text-transform: uppercase;
            display: inline-block;
            margin-top: 10px;
        }
        .grade-excellent { background: rgba(255,255,255,0.2); }
        .grade-very-good { background: rgba(255,255,255,0.2); }
        .grade-good { background: rgba(255,255,255,0.2); }
        .grade-fair { background: rgba(255,255,255,0.2); }
        .grade-poor { background: rgba(255,255,255,0.2); }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .summary-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 20px;
            color: white;
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        
        .question-review {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .review-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 20px;
            border-radius: 12px 12px 0 0;
        }
        .question-item {
            padding: 25px;
            border-bottom: 1px solid #e9ecef;
        }
        .question-item:last-child {
            border-bottom: none;
        }
        .question-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            font-weight: 600;
            margin-right: 15px;
            color: white;
        }
        .question-number.correct {
            background: #28a745;
        }
        .question-number.incorrect {
            background: #dc3545;
        }
        .question-text {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 15px;
        }
        .options-grid {
            display: grid;
            gap: 10px;
            margin-bottom: 15px;
        }
        .option-item {
            padding: 12px 15px;
            border-radius: 8px;
            border: 2px solid transparent;
        }
        .option-item.correct {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .option-item.selected-wrong {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .option-item.neutral {
            background: #f8f9fa;
            color: #6c757d;
        }
        .explanation {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            margin-top: 15px;
        }
        .difficulty-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .difficulty-easy { background: #d4edda; color: #155724; }
        .difficulty-medium { background: #fff3cd; color: #856404; }
        .difficulty-hard { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Student Portal</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>

            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="quiz.php" class="nav-item">
                    <i class="fas fa-brain"></i>
                    <span>Take Quiz</span>
                </a>
                <a href="results.php" class="nav-item active">
                    <i class="fas fa-chart-line"></i>
                    <span>My Results</span>
                </a>
                <a href="profile.php" class="nav-item">
                    <i class="fas fa-user"></i>
                    <span>Profile</span>
                </a>
                <a href="logout.php" class="nav-item">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <h4><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h4>
                        <p><?php echo htmlspecialchars($student['student_id']); ?></p>
                        <p><?php echo htmlspecialchars($student['department_name'] . ' - ' . $student['level_name']); ?></p>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="result-container">
                <!-- Result Header -->
                <div class="result-header">
                    <h1><i class="fas fa-trophy"></i> Quiz Result</h1>
                    <p><?php echo htmlspecialchars($quiz['department_name'] . ' - ' . $quiz['level_name']); ?></p>
                    <div class="score-display"><?php echo number_format($quiz['score'], 1); ?>%</div>
                    <div class="grade-badge grade-<?php echo strtolower(str_replace(' ', '-', $quiz['grade'])); ?>">
                        <?php echo $quiz['grade']; ?>
                    </div>
                    <p style="margin-top: 15px; opacity: 0.9;">
                        Completed on <?php echo date('F j, Y \a\t g:i A', strtotime($quiz['completed_at'])); ?>
                    </p>
                </div>

                <!-- Summary Cards -->
                <div class="summary-grid">
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        <h3><?php echo $quiz['correct_answers']; ?></h3>
                        <p>Correct Answers</p>
                    </div>

                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-times"></i>
                        </div>
                        <h3><?php echo $quiz['total_questions'] - $quiz['correct_answers']; ?></h3>
                        <p>Incorrect Answers</p>
                    </div>

                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h3>
                            <?php 
                            $minutes = floor($quiz['time_taken'] / 60);
                            $seconds = $quiz['time_taken'] % 60;
                            echo sprintf('%02d:%02d', $minutes, $seconds);
                            ?>
                        </h3>
                        <p>Time Taken</p>
                    </div>

                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <h3><?php echo number_format(($quiz['correct_answers'] / $quiz['total_questions']) * 100, 1); ?>%</h3>
                        <p>Accuracy Rate</p>
                    </div>
                </div>

                <!-- Question Review -->
                <div class="question-review">
                    <div class="review-header">
                        <h3><i class="fas fa-list"></i> Question by Question Review</h3>
                    </div>
                    
                    <?php foreach ($answers as $answer): ?>
                        <div class="question-item">
                            <div style="display: flex; align-items: flex-start; margin-bottom: 15px;">
                                <span class="question-number <?php echo $answer['is_correct'] ? 'correct' : 'incorrect'; ?>">
                                    <?php echo $answer['question_number']; ?>
                                </span>
                                <div style="flex: 1;">
                                    <div class="question-text">
                                        <?php echo htmlspecialchars($answer['question_text']); ?>
                                    </div>
                                    <span class="difficulty-badge difficulty-<?php echo $answer['difficulty']; ?>">
                                        <?php echo ucfirst($answer['difficulty']); ?>
                                    </span>
                                </div>
                            </div>

                            <div class="options-grid">
                                <?php 
                                $options = ['A' => $answer['option_a'], 'B' => $answer['option_b'], 'C' => $answer['option_c'], 'D' => $answer['option_d']];
                                foreach ($options as $letter => $text): 
                                    $class = 'neutral';
                                    if ($letter === $answer['correct_answer']) {
                                        $class = 'correct';
                                    } elseif ($letter === $answer['selected_answer'] && !$answer['is_correct']) {
                                        $class = 'selected-wrong';
                                    }
                                ?>
                                    <div class="option-item <?php echo $class; ?>">
                                        <strong><?php echo $letter; ?>.</strong> <?php echo htmlspecialchars($text); ?>
                                        <?php if ($letter === $answer['correct_answer']): ?>
                                            <i class="fas fa-check" style="float: right; color: #28a745;"></i>
                                        <?php elseif ($letter === $answer['selected_answer'] && !$answer['is_correct']): ?>
                                            <i class="fas fa-times" style="float: right; color: #dc3545;"></i>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <?php if (!empty($answer['explanation'])): ?>
                                <div class="explanation">
                                    <strong><i class="fas fa-lightbulb"></i> Explanation:</strong><br>
                                    <?php echo htmlspecialchars($answer['explanation']); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Action Buttons -->
                <div style="text-align: center; margin-top: 30px;">
                    <a href="results.php" class="btn btn-secondary" style="margin-right: 15px;">
                        <i class="fas fa-arrow-left"></i> Back to Results
                    </a>
                    <a href="quiz.php" class="btn btn-primary">
                        <i class="fas fa-brain"></i> Take Another Quiz
                    </a>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
