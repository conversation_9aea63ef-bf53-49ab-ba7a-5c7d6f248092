<?php
/**
 * Test AI Question Generation
 */

require_once 'config/database.php';

echo "<h1>AI Question Generation Test</h1>";

// Check if we have departments and subjects
$departments = fetchAll("SELECT * FROM departments WHERE status = 'active' LIMIT 3");
$subjects = fetchAll("SELECT * FROM subjects WHERE status = 'active' LIMIT 5");

echo "<h2>Available Data:</h2>";
echo "<p>Departments: " . count($departments) . "</p>";
echo "<p>Subjects: " . count($subjects) . "</p>";

if (count($departments) > 0) {
    echo "<h2>Test AI Question Generation:</h2>";
    
    foreach ($departments as $dept) {
        echo "<div style='margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px;'>";
        echo "<h3>{$dept['name']} ({$dept['code']})</h3>";
        echo "<p>{$dept['description']}</p>";
        
        // Get subjects for this department
        $deptSubjects = fetchAll("SELECT * FROM subjects WHERE department_id = ? AND status = 'active'", [$dept['id']]);
        echo "<p>Subjects in this department: " . count($deptSubjects) . "</p>";
        
        if (count($deptSubjects) > 0) {
            echo "<button onclick=\"generateForDepartment({$dept['id']})\" style='padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;'>Generate 5 Questions</button>";
        }
        
        echo "</div>";
    }
} else {
    echo "<p style='color: red;'>No departments found. Please run setup-sample-data.php first.</p>";
}

// Check existing questions
$existingQuestions = fetchAll("SELECT COUNT(*) as count FROM questions")[0]['count'];
echo "<h2>Current Questions in Database: {$existingQuestions}</h2>";

if ($existingQuestions > 0) {
    $sampleQuestions = fetchAll("SELECT q.*, s.name as subject_name, d.name as dept_name 
                                FROM questions q 
                                LEFT JOIN subjects s ON q.subject_id = s.id 
                                LEFT JOIN departments d ON q.department_id = d.id 
                                ORDER BY q.created_at DESC LIMIT 5");
    
    echo "<h3>Recent Questions:</h3>";
    foreach ($sampleQuestions as $q) {
        echo "<div style='margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 5px;'>";
        echo "<strong>Q:</strong> " . htmlspecialchars($q['question_text']) . "<br>";
        echo "<strong>Subject:</strong> " . htmlspecialchars($q['subject_name'] ?? 'General') . "<br>";
        echo "<strong>Department:</strong> " . htmlspecialchars($q['dept_name'] ?? 'General') . "<br>";
        echo "<strong>Difficulty:</strong> " . htmlspecialchars($q['difficulty']) . "<br>";
        echo "<strong>Source:</strong> " . htmlspecialchars($q['source'] ?? 'Manual') . "<br>";
        echo "</div>";
    }
}

?>

<script>
function generateForDepartment(deptId) {
    const button = event.target;
    button.disabled = true;
    button.textContent = 'Generating...';
    
    const formData = new FormData();
    formData.append('action', 'generate_questions');
    formData.append('department_id', deptId);
    formData.append('questions_per_subject', 5);
    
    fetch('admin/generate-questions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        button.disabled = false;
        button.textContent = 'Generate 5 Questions';
        
        if (data.success) {
            alert(`Success! Generated ${data.total_generated} questions for ${data.processed_departments.length} departments.`);
            location.reload();
        } else {
            alert('Error: ' + (data.error || 'Unknown error occurred'));
        }
    })
    .catch(error => {
        button.disabled = false;
        button.textContent = 'Generate 5 Questions';
        alert('Error: ' + error.message);
    });
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
button:hover { background: #0056b3 !important; }
</style>
