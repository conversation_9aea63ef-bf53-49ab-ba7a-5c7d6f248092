<?php
/**
 * API Key Setup Guide for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Require admin login
requireLogin('admin');

$pageTitle = 'API Key Setup Guide';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="../assets/images/logo.png" alt="Logo" onerror="this.style.display='none'">
                </div>
                <div class="admin-info">
                    <h3>Admin Panel</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>

            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="students.php" class="nav-item">
                    <i class="fas fa-users"></i>
                    <span>Students</span>
                </a>
                <a href="departments.php" class="nav-item">
                    <i class="fas fa-building"></i>
                    <span>Departments</span>
                </a>
                <a href="subjects.php" class="nav-item">
                    <i class="fas fa-book"></i>
                    <span>Subjects</span>
                </a>
                <a href="questions.php" class="nav-item">
                    <i class="fas fa-question-circle"></i>
                    <span>Questions</span>
                </a>
                <a href="rewards.php" class="nav-item">
                    <i class="fas fa-trophy"></i>
                    <span>Rewards</span>
                </a>
                <a href="analytics.php" class="nav-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>Analytics</span>
                </a>
                <a href="settings.php" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
                
                <div class="nav-divider"></div>
                
                <a href="../index.php" class="nav-item nav-secondary">
                    <i class="fas fa-home"></i>
                    <span>Back to Site</span>
                </a>
                <a href="../logout.php" class="nav-item nav-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="content-header">
                <h1><i class="fas fa-key"></i> API Key Setup Guide</h1>
                <p>Configure your OpenAI API key for AI question generation</p>
            </div>

            <div class="content-body">
                <!-- Step-by-Step Guide -->
                <div class="setup-guide">
                    <div class="guide-card">
                        <div class="guide-header">
                            <h2><i class="fas fa-rocket"></i> Get Started with AI Question Generation</h2>
                            <p>Follow these simple steps to enable automatic question generation for your LMS</p>
                        </div>

                        <!-- Step 1 -->
                        <div class="setup-step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h3><i class="fas fa-user-plus"></i> Create OpenAI Account</h3>
                                <p>If you don't have an OpenAI account, you'll need to create one first.</p>
                                <div class="step-actions">
                                    <a href="https://platform.openai.com/signup" target="_blank" class="btn btn-primary">
                                        <i class="fas fa-external-link-alt"></i>
                                        Create OpenAI Account
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Step 2 -->
                        <div class="setup-step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h3><i class="fas fa-credit-card"></i> Add Billing Information</h3>
                                <p>OpenAI requires billing information to use their API. Don't worry - you only pay for what you use!</p>
                                <div class="pricing-info">
                                    <div class="pricing-card">
                                        <h4>💰 Estimated Costs</h4>
                                        <ul>
                                            <li>✅ 100 questions: ~$0.50</li>
                                            <li>✅ 1,000 questions: ~$5.00</li>
                                            <li>✅ Very affordable for educational use</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="step-actions">
                                    <a href="https://platform.openai.com/account/billing" target="_blank" class="btn btn-secondary">
                                        <i class="fas fa-external-link-alt"></i>
                                        Setup Billing
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Step 3 -->
                        <div class="setup-step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h3><i class="fas fa-key"></i> Generate API Key</h3>
                                <p>Create a new API key that your LMS will use to generate questions.</p>
                                <div class="warning-box">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <strong>Important:</strong> Copy your API key immediately - you won't be able to see it again!
                                </div>
                                <div class="step-actions">
                                    <a href="https://platform.openai.com/api-keys" target="_blank" class="btn btn-success">
                                        <i class="fas fa-external-link-alt"></i>
                                        Generate API Key
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Step 4 -->
                        <div class="setup-step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <h3><i class="fas fa-download"></i> Install Python Dependencies</h3>
                                <p>Run the setup script to install required Python packages for AI question generation.</p>
                                <div class="warning-box">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>One-time setup:</strong> This only needs to be done once on your server.
                                </div>
                                <div class="step-actions">
                                    <a href="../scripts/setup_ai.bat" class="btn btn-secondary" download>
                                        <i class="fas fa-download"></i>
                                        Download Setup Script
                                    </a>
                                </div>
                                <div style="margin-top: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 8px; font-family: monospace; font-size: 0.9rem;">
                                    <strong>Manual installation:</strong><br>
                                    1. Open command prompt in the scripts folder<br>
                                    2. Run: <code>pip install -r requirements.txt</code>
                                </div>
                            </div>
                        </div>

                        <!-- Step 5 -->
                        <div class="setup-step">
                            <div class="step-number">5</div>
                            <div class="step-content">
                                <h3><i class="fas fa-cog"></i> Configure in LMS</h3>
                                <p>Paste your API key in the LMS settings to enable AI question generation.</p>
                                <div class="step-actions">
                                    <a href="settings.php#ai" class="btn btn-primary">
                                        <i class="fas fa-arrow-right"></i>
                                        Go to Settings
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="quick-links">
                        <h3><i class="fas fa-link"></i> Quick Links</h3>
                        <div class="links-grid">
                            <a href="https://platform.openai.com/docs/quickstart" target="_blank" class="link-card">
                                <i class="fas fa-book"></i>
                                <span>OpenAI Documentation</span>
                            </a>
                            <a href="https://platform.openai.com/usage" target="_blank" class="link-card">
                                <i class="fas fa-chart-line"></i>
                                <span>Usage Dashboard</span>
                            </a>
                            <a href="settings.php" class="link-card">
                                <i class="fas fa-cog"></i>
                                <span>LMS Settings</span>
                            </a>
                            <a href="questions.php" class="link-card">
                                <i class="fas fa-question-circle"></i>
                                <span>Generate Questions</span>
                            </a>
                        </div>
                    </div>

                    <!-- FAQ -->
                    <div class="faq-section">
                        <h3><i class="fas fa-question-circle"></i> Frequently Asked Questions</h3>
                        
                        <div class="faq-item">
                            <h4>💰 How much does it cost?</h4>
                            <p>OpenAI charges based on usage. For educational purposes, costs are very low - typically $0.50-$5.00 for hundreds of questions.</p>
                        </div>

                        <div class="faq-item">
                            <h4>🔒 Is my API key secure?</h4>
                            <p>Yes! Your API key is encrypted and stored securely in the database. Only authorized admins can access it.</p>
                        </div>

                        <div class="faq-item">
                            <h4>🤖 What types of questions can be generated?</h4>
                            <p>The AI can generate department-specific multiple-choice questions covering a wide range of topics and difficulty levels.</p>
                        </div>

                        <div class="faq-item">
                            <h4>⚡ How fast is question generation?</h4>
                            <p>Questions are generated in real-time, typically taking 5-10 seconds for a batch of 10-20 questions.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
