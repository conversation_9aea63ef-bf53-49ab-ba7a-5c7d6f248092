<?php
/**
 * Test Gemini API Integration for Question Generation
 */

require_once '../config/database.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Test Gemini API</title>
    <style>
        body { font-family: <PERSON>l, sans-serif; margin: 20px; }
        .success { color: green; padding: 10px; background: #d4edda; border-radius: 5px; margin: 10px 0; }
        .error { color: red; padding: 10px; background: #f8d7da; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; padding: 10px; background: #d1ecf1; border-radius: 5px; margin: 10px 0; }
        .question { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>";

echo "<h1>🧪 Testing Gemini API Integration</h1>";

/**
 * Test Gemini API function
 */
function testGeminiAPI($apiKey) {
    $prompt = "Generate 2 multiple-choice questions for Computer Science students at ND1 level. 

Focus on practical and theoretical knowledge relevant to Computer Science curriculum at polytechnic level.

Return ONLY a valid JSON array with this exact structure:
[
  {
    \"question_text\": \"Question here?\",
    \"option_a\": \"First option\",
    \"option_b\": \"Second option\", 
    \"option_c\": \"Third option\",
    \"option_d\": \"Fourth option\",
    \"correct_answer\": \"A\",
    \"explanation\": \"Why this answer is correct\",
    \"difficulty\": \"medium\"
  }
]

Make questions diverse, practical, and appropriate for polytechnic ND1 students studying Computer Science.
Return only the JSON array, no additional text or formatting.";

    $data = [
        'contents' => [
            [
                'parts' => [
                    [
                        'text' => $prompt
                    ]
                ]
            ]
        ],
        'generationConfig' => [
            'temperature' => 0.7,
            'topK' => 40,
            'topP' => 0.95,
            'maxOutputTokens' => 3000
        ]
    ];

    echo "<div class='info'>📡 Making API request to Gemini...</div>";
    echo "<pre>Request URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=[API_KEY]</pre>";
    echo "<pre>Request Data: " . json_encode($data, JSON_PRETTY_PRINT) . "</pre>";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=' . $apiKey);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    echo "<div class='info'>📊 API Response Details:</div>";
    echo "<pre>HTTP Code: " . $httpCode . "</pre>";
    
    if ($curlError) {
        echo "<div class='error'>❌ cURL Error: " . $curlError . "</div>";
        return false;
    }

    echo "<pre>Raw Response: " . htmlspecialchars($response) . "</pre>";

    if ($httpCode === 200 && $response) {
        $result = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "<div class='error'>❌ JSON Decode Error: " . json_last_error_msg() . "</div>";
            return false;
        }

        echo "<div class='info'>📋 Parsed Response Structure:</div>";
        echo "<pre>" . json_encode($result, JSON_PRETTY_PRINT) . "</pre>";
        
        if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
            $content = trim($result['candidates'][0]['content']['parts'][0]['text']);
            
            echo "<div class='success'>✅ Content extracted successfully!</div>";
            echo "<pre>Extracted Content: " . htmlspecialchars($content) . "</pre>";
            
            // Clean up the response - remove markdown formatting if present
            $content = preg_replace('/```json\s*/', '', $content);
            $content = preg_replace('/```\s*$/', '', $content);
            $content = trim($content);
            
            echo "<div class='info'>🧹 Cleaned Content:</div>";
            echo "<pre>" . htmlspecialchars($content) . "</pre>";
            
            $questions = json_decode($content, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                echo "<div class='error'>❌ Questions JSON Decode Error: " . json_last_error_msg() . "</div>";
                return false;
            }
            
            if (is_array($questions) && !empty($questions)) {
                echo "<div class='success'>🎉 Questions parsed successfully!</div>";
                echo "<div class='info'>📝 Generated Questions:</div>";
                
                foreach ($questions as $index => $question) {
                    echo "<div class='question'>";
                    echo "<h4>Question " . ($index + 1) . ":</h4>";
                    echo "<p><strong>Q:</strong> " . htmlspecialchars($question['question_text'] ?? 'N/A') . "</p>";
                    echo "<p><strong>A:</strong> " . htmlspecialchars($question['option_a'] ?? 'N/A') . "</p>";
                    echo "<p><strong>B:</strong> " . htmlspecialchars($question['option_b'] ?? 'N/A') . "</p>";
                    echo "<p><strong>C:</strong> " . htmlspecialchars($question['option_c'] ?? 'N/A') . "</p>";
                    echo "<p><strong>D:</strong> " . htmlspecialchars($question['option_d'] ?? 'N/A') . "</p>";
                    echo "<p><strong>Correct:</strong> " . htmlspecialchars($question['correct_answer'] ?? 'N/A') . "</p>";
                    echo "<p><strong>Explanation:</strong> " . htmlspecialchars($question['explanation'] ?? 'N/A') . "</p>";
                    echo "<p><strong>Difficulty:</strong> " . htmlspecialchars($question['difficulty'] ?? 'N/A') . "</p>";
                    echo "</div>";
                }
                
                return $questions;
            } else {
                echo "<div class='error'>❌ Failed to parse questions as array or empty result</div>";
                return false;
            }
        } else {
            echo "<div class='error'>❌ Unexpected API response structure</div>";
            return false;
        }
    } else {
        echo "<div class='error'>❌ API request failed with HTTP code: " . $httpCode . "</div>";
        return false;
    }
}

// Test the API
$apiKey = "AIzaSyBx_2EL2AiM9UvEEusJPlAXsXN4HNgTkiw";
echo "<div class='info'>🔑 Using API Key: " . substr($apiKey, 0, 10) . "..." . substr($apiKey, -5) . "</div>";

$result = testGeminiAPI($apiKey);

if ($result) {
    echo "<div class='success'>🎉 <strong>SUCCESS!</strong> Gemini API is working correctly and generating questions!</div>";
    echo "<p><a href='quiz.php'>🧠 Go to Quiz Page to test full integration</a></p>";
} else {
    echo "<div class='error'>❌ <strong>FAILED!</strong> There's an issue with the Gemini API integration.</div>";
}

echo "</body></html>";
?>
