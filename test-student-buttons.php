<?php
/**
 * Test Student Management Buttons
 */

require_once 'config/database.php';

echo "<h1>Student Management Buttons Test</h1>";

// Get students for testing
$students = fetchAll("SELECT s.*, d.name as department_name, al.level_name 
                     FROM students s 
                     LEFT JOIN departments d ON s.department_id = d.id 
                     LEFT JOIN academic_levels al ON s.academic_level_id = al.id 
                     LIMIT 5");

echo "<h2>Available Students: " . count($students) . "</h2>";

if (count($students) == 0) {
    echo "<p style='color: orange;'>No students found. Creating test student...</p>";
    
    // Get first department and level
    $dept = fetchOne("SELECT * FROM departments LIMIT 1");
    $level = fetchOne("SELECT * FROM academic_levels LIMIT 1");
    
    if ($dept && $level) {
        try {
            $testStudentId = insertRecord('students', [
                'student_id' => 'TEST' . rand(100, 999),
                'first_name' => 'Test',
                'last_name' => 'Student',
                'email' => 'test' . rand(100, 999) . '@test.com',
                'phone' => '080' . rand(10000000, 99999999),
                'password_hash' => password_hash('password123', PASSWORD_DEFAULT),
                'department_id' => $dept['id'],
                'academic_level_id' => $level['id'],
                'is_approved' => 0,
                'total_points' => rand(50, 200)
            ]);
            
            if ($testStudentId) {
                // Create test quiz sessions
                for ($i = 1; $i <= 3; $i++) {
                    insertRecord('quiz_sessions', [
                        'student_id' => $testStudentId,
                        'score' => rand(5, 10),
                        'total_questions' => 10,
                        'time_taken' => rand(300, 600),
                        'created_at' => date('Y-m-d H:i:s', strtotime("-$i days"))
                    ]);
                }
                echo "<p style='color: green;'>✓ Test student created with quiz history</p>";
                
                // Refresh students list
                $students = fetchAll("SELECT s.*, d.name as department_name, al.level_name 
                                     FROM students s 
                                     LEFT JOIN departments d ON s.department_id = d.id 
                                     LEFT JOIN academic_levels al ON s.academic_level_id = al.id 
                                     LIMIT 5");
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>Error creating test student: " . $e->getMessage() . "</p>";
        }
    }
}

if (count($students) > 0) {
    echo "<h2>Test Student Management Functions:</h2>";
    
    foreach ($students as $student) {
        echo "<div style='margin: 20px 0; padding: 20px; border: 2px solid #ddd; border-radius: 10px; background: #f9f9f9;'>";
        echo "<h3>{$student['first_name']} {$student['last_name']} ({$student['student_id']})</h3>";
        echo "<p><strong>Department:</strong> " . ($student['department_name'] ?? 'Not Set') . "</p>";
        echo "<p><strong>Level:</strong> " . ($student['level_name'] ?? 'Not Set') . "</p>";
        echo "<p><strong>Status:</strong> " . ($student['is_approved'] ? '<span style="color: green;">Approved</span>' : '<span style="color: orange;">Pending</span>') . "</p>";
        echo "<p><strong>Points:</strong> {$student['total_points']}</p>";
        echo "<p><strong>Registered:</strong> " . date('M d, Y', strtotime($student['registration_date'])) . "</p>";
        
        echo "<div style='margin-top: 15px;'>";
        echo "<button onclick=\"testProgress({$student['id']})\" style='margin: 5px; padding: 10px 15px; background: #17a2b8; color: white; border: none; border-radius: 5px; cursor: pointer;'>
                <i class='fas fa-chart-line'></i> Test Progress
              </button>";
        
        if (!$student['is_approved']) {
            echo "<button onclick=\"testApprove({$student['id']})\" style='margin: 5px; padding: 10px 15px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;'>
                    <i class='fas fa-check'></i> Test Approve
                  </button>";
        }
        
        echo "<button onclick=\"testReset({$student['id']})\" style='margin: 5px; padding: 10px 15px; background: #ffc107; color: black; border: none; border-radius: 5px; cursor: pointer;'>
                <i class='fas fa-key'></i> Test Reset
              </button>";
        
        echo "<button onclick=\"testDelete({$student['id']})\" style='margin: 5px; padding: 10px 15px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer;'>
                <i class='fas fa-trash'></i> Test Delete
              </button>";
        echo "</div>";
        echo "</div>";
    }
}

// Check quiz sessions
$quizSessions = fetchAll("SELECT COUNT(*) as count FROM quiz_sessions")[0]['count'];
echo "<h2>Quiz Sessions in Database: {$quizSessions}</h2>";

// Check if AI questions exist
$aiQuestions = fetchAll("SELECT COUNT(*) as count FROM questions WHERE source = 'AI Generated'")[0]['count'];
echo "<h2>AI Generated Questions: {$aiQuestions}</h2>";

?>

<!-- Include Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<script>
function testProgress(studentId) {
    console.log('Testing progress for student:', studentId);
    
    // Show loading
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'loading-modal';
    loadingDiv.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
        background: rgba(0,0,0,0.5); display: flex; align-items: center; 
        justify-content: center; z-index: 1000;
    `;
    loadingDiv.innerHTML = '<div style="background: white; padding: 20px; border-radius: 10px;">Loading student progress...</div>';
    document.body.appendChild(loadingDiv);
    
    fetch('admin/ajax/student-progress.php?id=' + studentId)
        .then(response => response.text())
        .then(data => {
            document.body.removeChild(loadingDiv);
            
            // Create modal
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
                background: rgba(0,0,0,0.5); display: flex; align-items: center; 
                justify-content: center; z-index: 1000;
            `;
            modal.innerHTML = `
                <div style="background: white; padding: 20px; border-radius: 10px; max-width: 80%; max-height: 80%; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h2>Student Progress</h2>
                        <button onclick="this.closest('div').parentElement.parentElement.remove()" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer;">Close</button>
                    </div>
                    ${data}
                </div>
            `;
            document.body.appendChild(modal);
        })
        .catch(error => {
            document.body.removeChild(loadingDiv);
            alert('Error loading progress: ' + error.message);
        });
}

function testApprove(studentId) {
    if (confirm('Test approve this student?')) {
        fetch('admin/students.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=approve&student_id=' + studentId
        })
        .then(response => response.text())
        .then(data => {
            alert('Approve test completed');
            location.reload();
        })
        .catch(error => {
            alert('Error testing approve: ' + error.message);
        });
    }
}

function testReset(studentId) {
    if (confirm('Test reset password for this student?')) {
        fetch('admin/students.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=reset_password&id=' + studentId
        })
        .then(response => response.text())
        .then(data => {
            alert('Password reset test completed');
        })
        .catch(error => {
            alert('Error testing reset: ' + error.message);
        });
    }
}

function testDelete(studentId) {
    if (confirm('Test delete this student? (This will actually delete the student!)')) {
        fetch('admin/students.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=delete&student_id=' + studentId
        })
        .then(response => response.text())
        .then(data => {
            alert('Delete test completed');
            location.reload();
        })
        .catch(error => {
            alert('Error testing delete: ' + error.message);
        });
    }
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
button:hover { opacity: 0.8; }
</style>
