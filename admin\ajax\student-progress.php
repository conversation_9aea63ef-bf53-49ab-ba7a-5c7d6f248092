<?php
/**
 * AJAX endpoint for student progress details
 */

require_once '../../config/database.php';

// Check if admin is logged in
requireLogin('admin');

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo '<div class="error">Invalid student ID</div>';
    exit;
}

$studentId = (int)$_GET['id'];

try {
    // Get student details
    $student = fetchOne("
        SELECT s.*, d.name as department_name, al.level_name 
        FROM students s 
        JOIN departments d ON s.department_id = d.id 
        JOIN academic_levels al ON s.academic_level_id = al.id 
        WHERE s.id = :id
    ", ['id' => $studentId]);

    if (!$student) {
        echo '<div class="error">Student not found</div>';
        exit;
    }

    // Get quiz statistics
    $quizStats = fetchOne("
        SELECT 
            COUNT(*) as total_quizzes,
            AVG(score) as avg_score,
            AVG(total_questions) as avg_questions,
            MAX(score) as best_score,
            MAX(total_questions) as max_questions
        FROM quiz_sessions 
        WHERE student_id = :id
    ", ['id' => $studentId]);

    // Get recent quiz sessions
    $recentQuizzes = fetchAll("
        SELECT qs.*, d.name as department_name, al.level_name
        FROM quiz_sessions qs
        JOIN departments d ON qs.department_id = d.id
        JOIN academic_levels al ON qs.academic_level_id = al.id
        WHERE qs.student_id = :id
        ORDER BY qs.created_at DESC
        LIMIT 10
    ", ['id' => $studentId]);

    // Get department/level performance
    $levelPerformance = fetchAll("
        SELECT
            CONCAT(d.name, ' - ', al.level_name) as level_name,
            COUNT(*) as quiz_count,
            AVG(qs.score) as avg_score,
            AVG(qs.total_questions) as avg_questions,
            MAX(qs.score) as best_score
        FROM quiz_sessions qs
        JOIN departments d ON qs.department_id = d.id
        JOIN academic_levels al ON qs.academic_level_id = al.id
        WHERE qs.student_id = :id
        GROUP BY qs.department_id, qs.academic_level_id, d.name, al.level_name
        ORDER BY quiz_count DESC
    ", ['id' => $studentId]);

    // Calculate overall performance
    $totalQuizzes = $quizStats['total_quizzes'] ?? 0;
    $avgScore = $quizStats['avg_score'] ?? 0;
    $avgQuestions = $quizStats['avg_questions'] ?? 0;
    $overallPercentage = $avgQuestions > 0 ? ($avgScore / $avgQuestions) * 100 : 0;

} catch (Exception $e) {
    echo '<div class="error">Error loading student progress: ' . htmlspecialchars($e->getMessage()) . '</div>';
    exit;
}
?>

<div class="student-progress-details">
    <!-- Student Header -->
    <div class="progress-header">
        <div class="student-avatar-large">
            <i class="fas fa-user"></i>
        </div>
        <div class="student-details">
            <h2><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h2>
            <p class="student-id"><?php echo htmlspecialchars($student['student_id']); ?></p>
            <p class="student-dept"><?php echo htmlspecialchars($student['department_name'] . ' - ' . $student['level_name']); ?></p>
            <div class="student-status">
                <?php if ($student['is_approved']): ?>
                    <span class="status approved">
                        <i class="fas fa-check-circle"></i>
                        Approved
                    </span>
                <?php else: ?>
                    <span class="status pending">
                        <i class="fas fa-clock"></i>
                        Pending Approval
                    </span>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Progress Stats -->
    <div class="progress-stats">
        <div class="stat-item">
            <div class="stat-icon">
                <i class="fas fa-clipboard-list"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo number_format($totalQuizzes); ?></h3>
                <p>Total Quizzes</p>
            </div>
        </div>
        
        <div class="stat-item">
            <div class="stat-icon">
                <i class="fas fa-percentage"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo number_format($overallPercentage, 1); ?>%</h3>
                <p>Average Score</p>
            </div>
        </div>
        
        <div class="stat-item">
            <div class="stat-icon">
                <i class="fas fa-star"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo number_format($student['total_points']); ?></h3>
                <p>Total Points</p>
            </div>
        </div>
        
        <div class="stat-item">
            <div class="stat-icon">
                <i class="fas fa-trophy"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $quizStats['best_score'] ?? 0; ?></h3>
                <p>Best Score</p>
            </div>
        </div>
    </div>

    <!-- Level Performance -->
    <?php if (!empty($levelPerformance)): ?>
    <div class="level-performance">
        <h3><i class="fas fa-chart-bar"></i> Department & Level Performance</h3>
        <div class="performance-list">
            <?php foreach ($levelPerformance as $level): ?>
                <div class="performance-item">
                    <div class="level-info">
                        <h4><?php echo htmlspecialchars($level['level_name']); ?></h4>
                        <p><?php echo $level['quiz_count']; ?> quiz<?php echo $level['quiz_count'] != 1 ? 'es' : ''; ?></p>
                    </div>
                    <div class="performance-stats">
                        <div class="performance-bar">
                            <?php
                            $percentage = $level['avg_questions'] > 0 ? ($level['avg_score'] / $level['avg_questions']) * 100 : 0;
                            $barClass = $percentage >= 70 ? 'good' : ($percentage >= 50 ? 'average' : 'poor');
                            ?>
                            <div class="bar-fill <?php echo $barClass; ?>" style="width: <?php echo $percentage; ?>%"></div>
                        </div>
                        <span class="percentage"><?php echo number_format($percentage, 1); ?>%</span>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Recent Quiz Activity -->
    <?php if (!empty($recentQuizzes)): ?>
    <div class="recent-activity">
        <h3><i class="fas fa-history"></i> Recent Quiz Activity</h3>
        <div class="activity-list">
            <?php foreach ($recentQuizzes as $quiz): ?>
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                    <div class="activity-content">
                        <h4><?php echo htmlspecialchars($quiz['subject_name'] ?? 'General Quiz'); ?></h4>
                        <p>Score: <?php echo $quiz['score']; ?>/<?php echo $quiz['total_questions']; ?> 
                           (<?php echo round(($quiz['score'] / $quiz['total_questions']) * 100); ?>%)</p>
                        <span class="activity-date"><?php echo date('M d, Y H:i', strtotime($quiz['created_at'])); ?></span>
                    </div>
                    <div class="activity-score">
                        <?php 
                        $percentage = ($quiz['score'] / $quiz['total_questions']) * 100;
                        $scoreClass = $percentage >= 70 ? 'good' : ($percentage >= 50 ? 'average' : 'poor');
                        ?>
                        <span class="score-badge <?php echo $scoreClass; ?>">
                            <?php echo round($percentage); ?>%
                        </span>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Action Buttons -->
    <div class="progress-actions">
        <?php if (!$student['is_approved']): ?>
            <button class="btn btn-success" onclick="approveStudent(<?php echo $student['id']; ?>)">
                <i class="fas fa-check"></i>
                Approve Student
            </button>
        <?php endif; ?>
        <button class="btn btn-primary" onclick="viewFullProfile(<?php echo $student['id']; ?>)">
            <i class="fas fa-user"></i>
            View Full Profile
        </button>
        <button class="btn btn-info" onclick="sendMessage(<?php echo $student['id']; ?>)">
            <i class="fas fa-envelope"></i>
            Send Message
        </button>
    </div>
</div>

<style>
.student-progress-details {
    max-width: 100%;
}

.progress-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.student-avatar-large {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.student-details h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.5rem;
}

.progress-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.progress-stats .stat-item {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-stats .stat-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.subject-performance, .recent-activity {
    margin-bottom: 2rem;
}

.subject-performance h3, .recent-activity h3 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    color: #1a202c;
    font-weight: 600;
}

.performance-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    margin-bottom: 0.75rem;
}

.performance-bar {
    width: 100px;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-right: 1rem;
}

.bar-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.bar-fill.good { background: #48bb78; }
.bar-fill.average { background: #ed8936; }
.bar-fill.poor { background: #e53e3e; }

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    margin-bottom: 0.75rem;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #48bb78, #38a169);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.activity-content {
    flex: 1;
}

.activity-date {
    font-size: 0.875rem;
    color: #718096;
}

.progress-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    padding-top: 1.5rem;
    border-top: 1px solid #e2e8f0;
}
</style>
