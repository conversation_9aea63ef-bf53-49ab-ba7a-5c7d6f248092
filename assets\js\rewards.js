/**
 * Rewards Management JavaScript
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeRewardsPage();
});

function initializeRewardsPage() {
    // Initialize form validation
    initializeFormValidation();
    
    // Initialize keyboard shortcuts
    initializeKeyboardShortcuts();
    
    // Initialize tooltips
    initializeTooltips();
    
    // Initialize search functionality
    initializeSearch();
}

function initializeFormValidation() {
    const awardPointsForm = document.querySelector('#awardPointsModal form');
    const createBadgeForm = document.querySelector('#createBadgeModal form');
    
    if (awardPointsForm) {
        awardPointsForm.addEventListener('submit', function(e) {
            if (!validateAwardPointsForm()) {
                e.preventDefault();
            }
        });
    }
    
    if (createBadgeForm) {
        createBadgeForm.addEventListener('submit', function(e) {
            if (!validateCreateBadgeForm()) {
                e.preventDefault();
            }
        });
    }
}

function validateAwardPointsForm() {
    let isValid = true;
    
    const studentId = document.getElementById('student_id').value;
    const points = document.getElementById('points').value;
    const reason = document.getElementById('reason').value.trim();
    
    // Clear previous errors
    clearFormErrors();
    
    if (!studentId) {
        showFieldError('student_id', 'Please select a student');
        isValid = false;
    }
    
    if (!points || points < 1 || points > 1000) {
        showFieldError('points', 'Please enter points between 1 and 1000');
        isValid = false;
    }
    
    if (!reason || reason.length < 10) {
        showFieldError('reason', 'Please provide a detailed reason (at least 10 characters)');
        isValid = false;
    }
    
    return isValid;
}

function validateCreateBadgeForm() {
    let isValid = true;
    
    const name = document.getElementById('badge_name').value.trim();
    const description = document.getElementById('badge_description').value.trim();
    const pointsRequired = document.getElementById('points_required').value;
    
    // Clear previous errors
    clearFormErrors();
    
    if (!name || name.length < 3) {
        showFieldError('badge_name', 'Badge name must be at least 3 characters');
        isValid = false;
    }
    
    if (!description || description.length < 10) {
        showFieldError('badge_description', 'Description must be at least 10 characters');
        isValid = false;
    }
    
    if (!pointsRequired || pointsRequired < 1) {
        showFieldError('points_required', 'Points required must be at least 1');
        isValid = false;
    }
    
    return isValid;
}

function showFieldError(fieldId, message) {
    const field = document.getElementById(fieldId);
    const formGroup = field.closest('.form-group');
    
    let errorElement = formGroup.querySelector('.field-error');
    if (!errorElement) {
        errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        formGroup.appendChild(errorElement);
    }
    
    errorElement.textContent = message;
    field.classList.add('error');
}

function clearFormErrors() {
    const errorElements = document.querySelectorAll('.field-error');
    errorElements.forEach(element => element.remove());
    
    const errorFields = document.querySelectorAll('.error');
    errorFields.forEach(field => field.classList.remove('error'));
}

function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + P for award points
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            openAwardPointsModal();
        }
        
        // Ctrl + B for create badge
        if (e.ctrlKey && e.key === 'b') {
            e.preventDefault();
            openCreateBadgeModal();
        }
        
        // Escape to close modals
        if (e.key === 'Escape') {
            closeAwardPointsModal();
            closeCreateBadgeModal();
        }
    });
}

function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[title]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function initializeSearch() {
    // Add search functionality for students in award points modal
    const studentSelect = document.getElementById('student_id');
    if (studentSelect) {
        // Convert select to searchable dropdown (basic implementation)
        studentSelect.addEventListener('focus', function() {
            this.size = Math.min(this.options.length, 8);
        });
        
        studentSelect.addEventListener('blur', function() {
            setTimeout(() => {
                this.size = 1;
            }, 200);
        });
    }
}

function showTooltip(event) {
    const element = event.target.closest('[title]');
    const title = element.getAttribute('title');
    
    if (title) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = title;
        tooltip.style.position = 'absolute';
        tooltip.style.zIndex = '1000';
        tooltip.style.backgroundColor = '#333';
        tooltip.style.color = 'white';
        tooltip.style.padding = '5px 10px';
        tooltip.style.borderRadius = '4px';
        tooltip.style.fontSize = '12px';
        tooltip.style.whiteSpace = 'nowrap';
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
        
        element._tooltip = tooltip;
    }
}

function hideTooltip(event) {
    const element = event.target.closest('[title]');
    if (element._tooltip) {
        document.body.removeChild(element._tooltip);
        element._tooltip = null;
    }
}

function openAwardPointsModal() {
    const modal = document.getElementById('awardPointsModal');
    const form = document.querySelector('#awardPointsModal form');
    
    // Reset form
    form.reset();
    clearFormErrors();
    
    // Set default points
    document.getElementById('points').value = 10;
    
    // Show modal
    modal.style.display = 'block';
    
    // Focus on student selection
    setTimeout(() => {
        document.getElementById('student_id').focus();
    }, 100);
}

function closeAwardPointsModal() {
    const modal = document.getElementById('awardPointsModal');
    modal.style.display = 'none';
    
    // Clear form
    document.querySelector('#awardPointsModal form').reset();
    clearFormErrors();
}

function openCreateBadgeModal() {
    const modal = document.getElementById('createBadgeModal');
    const form = document.querySelector('#createBadgeModal form');
    
    // Reset form
    form.reset();
    clearFormErrors();
    
    // Set default values
    document.getElementById('badge_icon').value = 'fas fa-medal';
    document.getElementById('points_required').value = 100;
    
    // Show modal
    modal.style.display = 'block';
    
    // Focus on badge name
    setTimeout(() => {
        document.getElementById('badge_name').focus();
    }, 100);
}

function closeCreateBadgeModal() {
    const modal = document.getElementById('createBadgeModal');
    modal.style.display = 'none';
    
    // Clear form
    document.querySelector('#createBadgeModal form').reset();
    clearFormErrors();
}

function awardBadgeModal(badgeId) {
    // Create a dynamic modal for awarding specific badge
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'block';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>Award Badge to Student</h3>
                <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
            </div>
            <div class="modal-body">
                <form method="POST">
                    <input type="hidden" name="action" value="award_badge">
                    <input type="hidden" name="badge_id" value="${badgeId}">
                    
                    <div class="form-group">
                        <label for="award_student_id">Select Student *</label>
                        <select id="award_student_id" name="student_id" required>
                            <option value="">Choose a student</option>
                            ${getStudentOptions()}
                        </select>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-medal"></i>
                            Award Badge
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
    
    // Focus on student selection
    setTimeout(() => {
        document.getElementById('award_student_id').focus();
    }, 100);
}

function getStudentOptions() {
    // Get student options from the existing select element
    const studentSelect = document.getElementById('student_id');
    let options = '';
    
    for (let i = 1; i < studentSelect.options.length; i++) {
        const option = studentSelect.options[i];
        options += `<option value="${option.value}">${option.textContent}</option>`;
    }
    
    return options;
}

function viewStudentProgress(studentId) {
    // Fetch and display student progress details
    fetch(`ajax/get-student-progress.php?id=${studentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showStudentProgressModal(data.student);
            } else {
                showErrorMessage('Error loading student progress');
            }
        })
        .catch(error => {
            showErrorMessage('Error loading student progress: ' + error.message);
        });
}

function showStudentProgressModal(student) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'block';
    modal.innerHTML = `
        <div class="modal-content large">
            <div class="modal-header">
                <h3>${student.first_name} ${student.last_name} - Progress Details</h3>
                <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="student-progress-details">
                    <div class="progress-overview">
                        <div class="progress-stat">
                            <label>Total Points</label>
                            <value class="points-value">${student.total_points}</value>
                        </div>
                        <div class="progress-stat">
                            <label>Quizzes Completed</label>
                            <value>${student.completed_quizzes}</value>
                        </div>
                        <div class="progress-stat">
                            <label>Average Score</label>
                            <value>${Math.round(student.avg_score || 0)}%</value>
                        </div>
                        <div class="progress-stat">
                            <label>Badges Earned</label>
                            <value>${student.badge_count}</value>
                        </div>
                    </div>
                    
                    <div class="recent-activity">
                        <h4>Recent Quiz Activity</h4>
                        <div class="activity-list">
                            ${student.recent_quizzes.map(quiz => `
                                <div class="activity-item">
                                    <div class="activity-info">
                                        <strong>${quiz.subject_name}</strong>
                                        <small>${new Date(quiz.created_at).toLocaleDateString()}</small>
                                    </div>
                                    <div class="activity-score">
                                        <span class="score-badge ${quiz.score >= 70 ? 'good' : 'average'}">${Math.round(quiz.score/quiz.total_questions*100)}%</span>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    
                    <div class="earned-badges">
                        <h4>Earned Badges</h4>
                        <div class="badges-list">
                            ${student.badges.map(badge => `
                                <div class="badge-item earned">
                                    <i class="${badge.icon}"></i>
                                    <span>${badge.name}</span>
                                    <small>Earned ${new Date(badge.earned_at).toLocaleDateString()}</small>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

function refreshLeaderboard() {
    // Refresh the leaderboard data
    fetch('ajax/refresh-leaderboard.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateLeaderboardDisplay(data.leaderboard);
                showSuccessMessage('Leaderboard refreshed');
            } else {
                showErrorMessage('Error refreshing leaderboard');
            }
        })
        .catch(error => {
            showErrorMessage('Error refreshing leaderboard: ' + error.message);
        });
}

function updateLeaderboardDisplay(leaderboard) {
    const leaderboardContainer = document.querySelector('.leaderboard');
    if (!leaderboardContainer) return;
    
    leaderboardContainer.innerHTML = leaderboard.map((student, index) => `
        <div class="leaderboard-item">
            <div class="rank">
                ${index === 0 ? '<i class="fas fa-crown gold"></i>' :
                  index === 1 ? '<i class="fas fa-medal silver"></i>' :
                  index === 2 ? '<i class="fas fa-medal bronze"></i>' :
                  `<span class="rank-number">${index + 1}</span>`}
            </div>
            <div class="student-info">
                <strong>${student.first_name} ${student.last_name}</strong>
                <small>${student.department_name}</small>
            </div>
            <div class="points">
                <span class="points-value">${student.total_points}</span>
                <small>points</small>
            </div>
            <div class="badges">
                <i class="fas fa-medal"></i>
                <span>${student.badge_count}</span>
            </div>
        </div>
    `).join('');
}

function showErrorMessage(message) {
    const alert = document.createElement('div');
    alert.className = 'alert alert-error';
    alert.innerHTML = `
        <i class="fas fa-exclamation-circle"></i>
        ${message}
    `;
    
    const mainContent = document.querySelector('.main-content');
    const header = mainContent.querySelector('.content-header');
    
    mainContent.insertBefore(alert, header.nextSibling);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.parentNode.removeChild(alert);
        }
    }, 5000);
}

function showSuccessMessage(message) {
    const alert = document.createElement('div');
    alert.className = 'alert alert-success';
    alert.innerHTML = `
        <i class="fas fa-check-circle"></i>
        ${message}
    `;
    
    const mainContent = document.querySelector('.main-content');
    const header = mainContent.querySelector('.content-header');
    
    mainContent.insertBefore(alert, header.nextSibling);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.parentNode.removeChild(alert);
        }
    }, 5000);
}

// Close modals when clicking outside
document.addEventListener('click', function(e) {
    const awardPointsModal = document.getElementById('awardPointsModal');
    const createBadgeModal = document.getElementById('createBadgeModal');
    
    if (e.target === awardPointsModal) {
        closeAwardPointsModal();
    }
    
    if (e.target === createBadgeModal) {
        closeCreateBadgeModal();
    }
});

// Export functions for global access
window.openAwardPointsModal = openAwardPointsModal;
window.closeAwardPointsModal = closeAwardPointsModal;
window.openCreateBadgeModal = openCreateBadgeModal;
window.closeCreateBadgeModal = closeCreateBadgeModal;
window.awardBadgeModal = awardBadgeModal;
window.viewStudentProgress = viewStudentProgress;
window.refreshLeaderboard = refreshLeaderboard;
