<?php
/**
 * Landing Page for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once 'config/database.php';

// Initialize default values
$stats = [
    'total_students' => 0,
    'total_departments' => 31,
    'total_questions' => 0,
    'active_today' => 0
];

$schoolName = 'Ogbonnaya Onu Polytechnic, Aba';
$schoolMotto = 'Excellence in Technical Education';

// Try to get system statistics (only if database is set up)
try {
    $database = new Database();
    $conn = $database->getConnection();

    if ($conn) {
        // Get system statistics for display
        $stats = [
            'total_students' => fetchOne("SELECT COUNT(*) as count FROM students WHERE is_approved = 1")['count'] ?? 0,
            'total_departments' => fetchOne("SELECT COUNT(*) as count FROM departments")['count'] ?? 31,
            'total_questions' => fetchOne("SELECT COUNT(*) as count FROM questions")['count'] ?? 0,
            'active_today' => fetchOne("SELECT COUNT(DISTINCT user_id) as count FROM user_sessions WHERE DATE(created_at) = CURDATE() AND user_type = 'student'")['count'] ?? 0
        ];

        // Get school settings
        $schoolName = fetchOne("SELECT setting_value FROM system_settings WHERE setting_key = 'school_name'")['setting_value'] ?? 'Ogbonnaya Onu Polytechnic, Aba';
        $schoolMotto = fetchOne("SELECT setting_value FROM system_settings WHERE setting_key = 'school_motto'")['setting_value'] ?? 'Excellence in Technical Education';
    }
} catch (Exception $e) {
    // Database not set up yet, use default values
    error_log("Database not ready: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI-Powered LMS - <?php echo htmlspecialchars($schoolName); ?></title>
    <meta name="description" content="AI-Powered Learning Management System for Ogbonnaya Onu Polytechnic students. Gamified learning with personalized quizzes and rewards.">
    <link rel="stylesheet" href="assets/css/landing.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <img src="images/logo.jpg" alt="School Logo" class="nav-logo">
                <div class="brand-text">
                    <h3>AI-Powered LMS</h3>
                    <p>Smart Learning Platform</p>
                </div>
            </div>
            
            <div class="nav-menu">
                <a href="#home" class="nav-link active">Home</a>
                <a href="#features" class="nav-link">Features</a>
                <a href="#about" class="nav-link">About</a>
                <a href="#stats" class="nav-link">Statistics</a>
            </div>
            
            <div class="nav-actions">
                <a href="student/login.php" class="btn btn-outline">
                    <i class="fas fa-graduation-cap"></i>
                    Student Login
                </a>
                <a href="admin/login.php" class="btn btn-primary">
                    <i class="fas fa-user-shield"></i>
                    Admin
                </a>
            </div>
            
            <div class="mobile-menu-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>
    
    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-robot"></i>
                    <span>AI-Powered Learning</span>
                </div>
                
                <h1 class="hero-title">
                    Smart Learning for
                    <span class="gradient-text">Future Leaders</span>
                </h1>
                
                <p class="hero-description">
                    Experience the future of education with our AI-powered Learning Management System. 
                    Personalized quizzes, gamified learning, and real-time progress tracking designed 
                    specifically for <?php echo htmlspecialchars($schoolName); ?> students.
                </p>
                
                <div class="hero-actions">
                    <a href="student/register.php" class="btn btn-primary btn-large">
                        <i class="fas fa-rocket"></i>
                        Start Learning Today
                    </a>
                    <a href="#features" class="btn btn-outline btn-large">
                        <i class="fas fa-play"></i>
                        Explore Features
                    </a>
                </div>
                
                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number"><?php echo number_format($stats['total_students']); ?>+</span>
                        <span class="stat-label">Active Students</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number"><?php echo $stats['total_departments']; ?></span>
                        <span class="stat-label">Departments</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number"><?php echo number_format($stats['total_questions']); ?>+</span>
                        <span class="stat-label">AI Questions</span>
                    </div>
                </div>
            </div>
            
            <div class="hero-visual">
                <div class="hero-image">
                    <img src="images/image2.jpg" alt="School Building" class="school-image">
                    <div class="floating-elements">
                        <div class="floating-card card-1">
                            <i class="fas fa-brain"></i>
                            <span>AI Learning</span>
                        </div>
                        <div class="floating-card card-2">
                            <i class="fas fa-trophy"></i>
                            <span>Rewards</span>
                        </div>
                        <div class="floating-card card-3">
                            <i class="fas fa-chart-line"></i>
                            <span>Progress</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-header">
                <h2>Powerful Features for Modern Learning</h2>
                <p>Discover how our AI-powered platform transforms the educational experience</p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3>AI-Generated Questions</h3>
                    <p>Personalized multiple-choice questions tailored to your department and academic level using advanced AI algorithms.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-gamepad"></i>
                    </div>
                    <h3>Gamified Learning</h3>
                    <p>Earn points, badges, and rewards while learning. Track your progress with streaks and achievements.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h3>Real-time Analytics</h3>
                    <p>Monitor your learning progress with detailed analytics and performance insights.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>Department-Specific</h3>
                    <p>Content tailored for all 31 departments with level-appropriate questions and materials.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Secure Platform</h3>
                    <p>Advanced security features with encrypted data and secure authentication systems.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>Mobile Responsive</h3>
                    <p>Access your learning materials anywhere, anytime with our fully responsive design.</p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">
            <div class="about-content">
                <div class="about-text">
                    <h2>About Our Platform</h2>
                    <p class="about-subtitle"><?php echo htmlspecialchars($schoolMotto); ?></p>
                    
                    <p>Our AI-Powered Learning Management System represents the future of education at <?php echo htmlspecialchars($schoolName); ?>. Designed specifically for polytechnic education, our platform combines cutting-edge artificial intelligence with gamification to create an engaging and effective learning environment.</p>
                    
                    <div class="about-features">
                        <div class="about-feature">
                            <i class="fas fa-check-circle"></i>
                            <span>Personalized learning paths for each student</span>
                        </div>
                        <div class="about-feature">
                            <i class="fas fa-check-circle"></i>
                            <span>AI-generated questions based on curriculum</span>
                        </div>
                        <div class="about-feature">
                            <i class="fas fa-check-circle"></i>
                            <span>Comprehensive progress tracking and analytics</span>
                        </div>
                        <div class="about-feature">
                            <i class="fas fa-check-circle"></i>
                            <span>Gamified rewards and achievement system</span>
                        </div>
                    </div>
                    
                    <a href="student/register.php" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i>
                        Join Our Platform
                    </a>
                </div>
                
                <div class="about-image">
                    <img src="images/image2.jpg" alt="School Campus" class="campus-image">
                    <div class="image-overlay">
                        <div class="overlay-content">
                            <h4><?php echo htmlspecialchars($schoolName); ?></h4>
                            <p>Leading Technical Education</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Statistics Section -->
    <section id="stats" class="statistics">
        <div class="container">
            <div class="section-header">
                <h2>Platform Statistics</h2>
                <p>See how our community is growing and learning together</p>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number" data-target="<?php echo $stats['total_students']; ?>">0</h3>
                        <p>Registered Students</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number" data-target="<?php echo $stats['total_departments']; ?>">0</h3>
                        <p>Academic Departments</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number" data-target="<?php echo $stats['total_questions']; ?>">0</h3>
                        <p>AI-Generated Questions</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number" data-target="<?php echo $stats['active_today']; ?>">0</h3>
                        <p>Active Today</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <img src="images/logo.jpg" alt="School Logo" class="footer-logo">
                        <h4>AI-Powered LMS</h4>
                        <p><?php echo htmlspecialchars($schoolName); ?></p>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="student/login.php">Student Login</a></li>
                        <li><a href="student/register.php">Register</a></li>
                        <li><a href="admin/login.php">Admin Portal</a></li>
                        <li><a href="#features">Features</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Contact Support</a></li>
                        <li><a href="#">System Status</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Connect</h4>
                    <div class="social-links">
                        <a href="#" class="social-link"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2025 <?php echo htmlspecialchars($schoolName); ?>. All rights reserved.</p>
                <p>Powered by AI-Powered LMS Technology</p>
            </div>
        </div>
    </footer>
    
    <script src="assets/js/landing.js"></script>
</body>
</html>
