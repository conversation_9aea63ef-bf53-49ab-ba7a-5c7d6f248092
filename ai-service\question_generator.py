#!/usr/bin/env python3
"""
AI Question Generator Service for LMS
Ogbonnaya Onu Polytechnic, Aba

This is a sample Python service that can be extended to use actual AI APIs
like OpenAI, Anthropic, or Google AI for generating educational questions.
"""

import json
import sys
import random
from typing import List, Dict, Any

class QuestionGenerator:
    def __init__(self):
        self.subjects_knowledge = {
            'computer_science': {
                'programming': [
                    'variables and data types',
                    'control structures',
                    'functions and procedures',
                    'object-oriented programming',
                    'algorithms and complexity'
                ],
                'database': [
                    'relational database design',
                    'SQL queries',
                    'normalization',
                    'transactions and concurrency',
                    'database security'
                ]
            },
            'electrical_engineering': {
                'circuits': [
                    'Ohm\'s law',
                    '<PERSON><PERSON><PERSON>\'s laws',
                    'AC and DC circuits',
                    'capacitors and inductors',
                    'circuit analysis techniques'
                ],
                'electronics': [
                    'diodes and transistors',
                    'amplifiers',
                    'digital logic',
                    'microprocessors',
                    'signal processing'
                ]
            },
            'accountancy': {
                'financial': [
                    'accounting equation',
                    'journal entries',
                    'financial statements',
                    'depreciation methods',
                    'cash flow analysis'
                ],
                'cost': [
                    'cost classification',
                    'cost-volume-profit analysis',
                    'budgeting',
                    'variance analysis',
                    'activity-based costing'
                ]
            }
        }
    
    def generate_questions(self, subject: str, difficulty: str, count: int, topics: str = "") -> List[Dict[str, Any]]:
        """
        Generate educational questions for a given subject and difficulty level.
        
        In a real implementation, this would call an AI API like:
        - OpenAI GPT-4
        - Anthropic Claude
        - Google Gemini
        - Local LLM models
        """
        
        questions = []
        subject_key = self._get_subject_key(subject)
        
        for i in range(count):
            question = self._generate_single_question(subject, subject_key, difficulty, i + 1, topics)
            questions.append(question)
        
        return questions
    
    def _get_subject_key(self, subject: str) -> str:
        """Map subject names to knowledge base keys"""
        subject_lower = subject.lower()
        
        if 'computer' in subject_lower or 'programming' in subject_lower:
            return 'computer_science'
        elif 'electrical' in subject_lower or 'electronics' in subject_lower:
            return 'electrical_engineering'
        elif 'account' in subject_lower or 'finance' in subject_lower:
            return 'accountancy'
        else:
            return 'general'
    
    def _generate_single_question(self, subject: str, subject_key: str, difficulty: str, question_num: int, topics: str) -> Dict[str, Any]:
        """Generate a single multiple-choice question"""
        
        # Get relevant topics for the subject
        if subject_key in self.subjects_knowledge:
            available_topics = []
            for category, topic_list in self.subjects_knowledge[subject_key].items():
                available_topics.extend(topic_list)
            
            if available_topics:
                topic = random.choice(available_topics)
            else:
                topic = "general concepts"
        else:
            topic = topics if topics else "fundamental principles"
        
        # Generate question based on difficulty
        if difficulty == 'easy':
            question_text = f"What is the basic concept of {topic} in {subject}?"
            options = [
                f"The fundamental principle of {topic}",
                f"An advanced application of {topic}",
                f"A complex theory related to {topic}",
                f"An unrelated concept to {topic}"
            ]
        elif difficulty == 'medium':
            question_text = f"How does {topic} apply in practical {subject} scenarios?"
            options = [
                f"It provides a framework for understanding {topic}",
                f"It directly implements {topic} in real-world applications",
                f"It theoretically explains {topic} without practical use",
                f"It contradicts the principles of {topic}"
            ]
        else:  # hard
            question_text = f"Analyze the complex relationship between {topic} and advanced {subject} methodologies."
            options = [
                f"The relationship involves multiple interdependent factors of {topic}",
                f"There is no significant relationship between {topic} and {subject}",
                f"The relationship is purely theoretical without practical implications",
                f"The relationship only applies in specific edge cases of {topic}"
            ]
        
        # Shuffle options and set correct answer
        random.shuffle(options)
        correct_answer = ['A', 'B', 'C', 'D'][0]  # First option is always correct after shuffle
        
        return {
            'question_text': question_text,
            'option_a': options[0],
            'option_b': options[1],
            'option_c': options[2],
            'option_d': options[3],
            'correct_answer': correct_answer,
            'explanation': f"This is correct because {options[0].lower()} represents the core understanding of {topic} in {subject}.",
            'difficulty': difficulty,
            'topic': topic
        }

def main():
    """Main function to handle command line interface"""
    if len(sys.argv) < 4:
        print(json.dumps({
            'success': False,
            'error': 'Usage: python question_generator.py <subject> <difficulty> <count> [topics]'
        }))
        return
    
    subject = sys.argv[1]
    difficulty = sys.argv[2]
    count = int(sys.argv[3])
    topics = sys.argv[4] if len(sys.argv) > 4 else ""
    
    try:
        generator = QuestionGenerator()
        questions = generator.generate_questions(subject, difficulty, count, topics)
        
        response = {
            'success': True,
            'questions': questions,
            'generated_count': len(questions)
        }
        
        print(json.dumps(response, indent=2))
        
    except Exception as e:
        response = {
            'success': False,
            'error': str(e)
        }
        print(json.dumps(response))

if __name__ == "__main__":
    main()
