<?php
/**
 * Get Question Details AJAX Endpoint
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../../config/database.php';

// Set JSON header
header('Content-Type: application/json');

try {
    // Require admin login
    requireLogin('admin');
    
    // Get question ID
    $questionId = (int)($_GET['id'] ?? 0);
    
    if (!$questionId) {
        throw new Exception('Question ID is required');
    }
    
    // Get question details with subject info
    $question = fetchOne("
        SELECT q.*, s.name as subject_name, s.code as subject_code,
               d.name as department_name, al.level_name
        FROM questions q
        JOIN subjects s ON q.subject_id = s.id
        JOIN departments d ON s.department_id = d.id
        JOIN academic_levels al ON s.academic_level_id = al.id
        WHERE q.id = :id
    ", ['id' => $questionId]);
    
    if (!$question) {
        throw new Exception('Question not found');
    }
    
    // Get usage statistics for this question
    $usageStats = fetchOne("
        SELECT 
            COUNT(*) as times_used,
            COUNT(CASE WHEN qa.selected_answer = q.correct_answer THEN 1 END) as correct_answers,
            COUNT(CASE WHEN qa.selected_answer != q.correct_answer THEN 1 END) as incorrect_answers,
            (COUNT(CASE WHEN qa.selected_answer = q.correct_answer THEN 1 END) / COUNT(*) * 100) as success_rate
        FROM quiz_answers qa
        JOIN questions q ON qa.question_id = q.id
        WHERE qa.question_id = :id
    ", ['id' => $questionId]);
    
    // Get recent attempts for this question
    $recentAttempts = fetchAll("
        SELECT qa.*, s.first_name, s.last_name, s.student_id,
               qs.created_at as quiz_date
        FROM quiz_answers qa
        JOIN quiz_sessions qs ON qa.quiz_session_id = qs.id
        JOIN students s ON qs.student_id = s.id
        WHERE qa.question_id = :id
        ORDER BY qs.created_at DESC
        LIMIT 10
    ", ['id' => $questionId]);
    
    // Get answer distribution
    $answerDistribution = fetchAll("
        SELECT 
            selected_answer,
            COUNT(*) as count,
            (COUNT(*) / (SELECT COUNT(*) FROM quiz_answers WHERE question_id = :id) * 100) as percentage
        FROM quiz_answers
        WHERE question_id = :id
        GROUP BY selected_answer
        ORDER BY selected_answer
    ", ['id' => $questionId]);
    
    // Get similar questions (same subject and difficulty)
    $similarQuestions = fetchAll("
        SELECT q.id, q.question_text, q.difficulty
        FROM questions q
        WHERE q.subject_id = :subject_id 
        AND q.difficulty = :difficulty 
        AND q.id != :id
        ORDER BY q.created_at DESC
        LIMIT 5
    ", [
        'subject_id' => $question['subject_id'],
        'difficulty' => $question['difficulty'],
        'id' => $questionId
    ]);
    
    // Format the question data
    $formattedQuestion = [
        'id' => $question['id'],
        'question_text' => $question['question_text'],
        'option_a' => $question['option_a'],
        'option_b' => $question['option_b'],
        'option_c' => $question['option_c'],
        'option_d' => $question['option_d'],
        'correct_answer' => $question['correct_answer'],
        'explanation' => $question['explanation'],
        'difficulty' => $question['difficulty'],
        'subject_name' => $question['subject_name'],
        'subject_code' => $question['subject_code'],
        'department_name' => $question['department_name'],
        'level_name' => $question['level_name'],
        'created_at' => $question['created_at'],
        'updated_at' => $question['updated_at']
    ];
    
    // Format usage statistics
    $formattedStats = [
        'times_used' => (int)($usageStats['times_used'] ?? 0),
        'correct_answers' => (int)($usageStats['correct_answers'] ?? 0),
        'incorrect_answers' => (int)($usageStats['incorrect_answers'] ?? 0),
        'success_rate' => round($usageStats['success_rate'] ?? 0, 1)
    ];
    
    // Format answer distribution
    $formattedDistribution = [];
    foreach (['A', 'B', 'C', 'D'] as $option) {
        $found = false;
        foreach ($answerDistribution as $dist) {
            if ($dist['selected_answer'] === $option) {
                $formattedDistribution[$option] = [
                    'count' => (int)$dist['count'],
                    'percentage' => round($dist['percentage'], 1),
                    'is_correct' => $option === $question['correct_answer']
                ];
                $found = true;
                break;
            }
        }
        if (!$found) {
            $formattedDistribution[$option] = [
                'count' => 0,
                'percentage' => 0,
                'is_correct' => $option === $question['correct_answer']
            ];
        }
    }
    
    // Response data
    $response = [
        'success' => true,
        'question' => $formattedQuestion,
        'usage_stats' => $formattedStats,
        'recent_attempts' => $recentAttempts,
        'answer_distribution' => $formattedDistribution,
        'similar_questions' => $similarQuestions,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
