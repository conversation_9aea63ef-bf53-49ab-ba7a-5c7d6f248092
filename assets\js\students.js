/**
 * Students Management JavaScript
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeStudentsPage();
});

function initializeStudentsPage() {
    // Auto-refresh student data every 30 seconds
    setInterval(refreshStudentStats, 30000);

    // Initialize search functionality
    initializeSearch();

    // Initialize tooltips
    initializeTooltips();

    // Initialize view toggle
    initializeViewToggle();
}

function initializeSearch() {
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        let searchTimeout;
        
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                // Auto-submit form after 500ms of no typing
                this.closest('form').submit();
            }, 500);
        });
    }
}

function initializeTooltips() {
    // Add tooltips to action buttons
    const tooltipElements = document.querySelectorAll('[title]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(event) {
    const element = event.target.closest('[title]');
    const title = element.getAttribute('title');
    
    if (title) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = title;
        tooltip.style.position = 'absolute';
        tooltip.style.zIndex = '1000';
        tooltip.style.backgroundColor = '#333';
        tooltip.style.color = 'white';
        tooltip.style.padding = '5px 10px';
        tooltip.style.borderRadius = '4px';
        tooltip.style.fontSize = '12px';
        tooltip.style.whiteSpace = 'nowrap';
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
        
        element._tooltip = tooltip;
    }
}

function hideTooltip(event) {
    const element = event.target.closest('[title]');
    if (element._tooltip) {
        document.body.removeChild(element._tooltip);
        element._tooltip = null;
    }
}

function refreshStudentStats() {
    fetch('ajax/refresh-student-stats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateHeaderStats(data.stats);
            }
        })
        .catch(error => {
            console.error('Error refreshing student stats:', error);
        });
}

function updateHeaderStats(stats) {
    const statElements = document.querySelectorAll('.header-stats .stat-number');
    if (statElements.length >= 3) {
        statElements[0].textContent = stats.total || '0';
        statElements[1].textContent = stats.approved || '0';
        statElements[2].textContent = stats.pending || '0';
    }
}

function viewStudentProgress(studentId) {
    // Show loading state
    showLoadingModal('Loading student progress...');
    
    fetch(`ajax/student-progress.php?id=${studentId}`)
        .then(response => response.text())
        .then(html => {
            hideLoadingModal();
            showModal('Student Progress', html);
        })
        .catch(error => {
            hideLoadingModal();
            showErrorModal('Error loading student progress: ' + error.message);
        });
}

function resetPassword(studentId) {
    if (confirm('Are you sure you want to reset this student\'s password to "student123"?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="reset_password">
            <input type="hidden" name="id" value="${studentId}">
        `;
        
        document.body.appendChild(form);
        form.submit();
    }
}

function deleteStudent(studentId, studentName) {
    if (confirm(`Are you sure you want to delete ${studentName}? This action cannot be undone.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_student">
            <input type="hidden" name="id" value="${studentId}">
        `;
        
        document.body.appendChild(form);
        form.submit();
    }
}

function showLoadingModal(message) {
    const modal = document.createElement('div');
    modal.id = 'loadingModal';
    modal.className = 'modal';
    modal.style.display = 'block';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="loading-spinner"></div>
                <p>${message}</p>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

function hideLoadingModal() {
    const modal = document.getElementById('loadingModal');
    if (modal) {
        document.body.removeChild(modal);
    }
}

function showModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'block';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

function showErrorModal(message) {
    showModal('Error', `
        <div class="alert alert-error">
            <i class="fas fa-exclamation-circle"></i>
            ${message}
        </div>
    `);
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl + F for search
    if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        const searchInput = document.querySelector('input[name="search"]');
        if (searchInput) {
            searchInput.focus();
        }
    }
    
    // Escape to close modals
    if (e.key === 'Escape') {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => modal.remove());
    }
});

// Export approval/rejection functions
function approveStudent(studentId) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `
        <input type="hidden" name="action" value="approve_student">
        <input type="hidden" name="id" value="${studentId}">
    `;
    
    document.body.appendChild(form);
    form.submit();
}

function rejectStudent(studentId) {
    if (confirm('Are you sure you want to revoke approval for this student?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="reject_student">
            <input type="hidden" name="id" value="${studentId}">
        `;
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Bulk actions
function selectAllStudents() {
    const checkboxes = document.querySelectorAll('.student-checkbox');
    const selectAllCheckbox = document.getElementById('selectAll');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
    
    updateBulkActions();
}

function updateBulkActions() {
    const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
    const bulkActions = document.querySelector('.bulk-actions');
    
    if (bulkActions) {
        if (checkedBoxes.length > 0) {
            bulkActions.style.display = 'block';
            bulkActions.querySelector('.selected-count').textContent = checkedBoxes.length;
        } else {
            bulkActions.style.display = 'none';
        }
    }
}

function bulkApproveStudents() {
    const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
    const studentIds = Array.from(checkedBoxes).map(cb => cb.value);
    
    if (studentIds.length === 0) {
        alert('Please select students to approve');
        return;
    }
    
    if (confirm(`Are you sure you want to approve ${studentIds.length} students?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="bulk_approve">
            <input type="hidden" name="student_ids" value="${studentIds.join(',')}">
        `;
        
        document.body.appendChild(form);
        form.submit();
    }
}

function bulkRejectStudents() {
    const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
    const studentIds = Array.from(checkedBoxes).map(cb => cb.value);
    
    if (studentIds.length === 0) {
        alert('Please select students to reject');
        return;
    }
    
    if (confirm(`Are you sure you want to revoke approval for ${studentIds.length} students?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="bulk_reject">
            <input type="hidden" name="student_ids" value="${studentIds.join(',')}">
        `;
        
        document.body.appendChild(form);
        form.submit();
    }
}

function initializeViewToggle() {
    const toggleButtons = document.querySelectorAll('.btn-toggle');
    const gridView = document.querySelector('.students-grid-view');
    const tableView = document.querySelector('.students-table-view');

    if (!toggleButtons.length || !gridView || !tableView) return;

    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const view = this.dataset.view;

            // Update button states
            toggleButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Toggle views
            if (view === 'grid') {
                gridView.classList.add('active');
                tableView.classList.remove('active');
                localStorage.setItem('studentsView', 'grid');
            } else {
                gridView.classList.remove('active');
                tableView.classList.add('active');
                localStorage.setItem('studentsView', 'table');
            }
        });
    });

    // Restore saved view preference
    const savedView = localStorage.getItem('studentsView') || 'grid';
    const savedButton = document.querySelector(`[data-view="${savedView}"]`);
    if (savedButton) {
        savedButton.click();
    }
}

// Export functions for global access
window.viewStudentProgress = viewStudentProgress;
window.resetPassword = resetPassword;
window.deleteStudent = deleteStudent;
window.approveStudent = approveStudent;
window.rejectStudent = rejectStudent;
window.selectAllStudents = selectAllStudents;
window.updateBulkActions = updateBulkActions;
window.bulkApproveStudents = bulkApproveStudents;
window.bulkRejectStudents = bulkRejectStudents;
