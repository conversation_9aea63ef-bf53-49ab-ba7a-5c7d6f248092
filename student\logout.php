<?php
/**
 * Student Logout for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Start session
startSecureSession();

// Check if user is logged in as student
if (isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'student') {
    // Remove session from database if session ID exists
    if (isset($_SESSION['session_id'])) {
        execute("DELETE FROM user_sessions WHERE id = :session_id", [
            'session_id' => $_SESSION['session_id']
        ]);
    }
    
    // Log the logout activity
    if (isset($_SESSION['user_id'])) {
        execute("INSERT INTO activity_logs (user_id, user_type, action, details, ip_address, created_at) VALUES (:user_id, :user_type, :action, :details, :ip, NOW())", [
            'user_id' => $_SESSION['user_id'],
            'user_type' => 'student',
            'action' => 'logout',
            'details' => 'Student logged out successfully',
            'ip' => $_SERVER['REMOTE_ADDR']
        ]);
    }
}

// Clear all session data
session_unset();
session_destroy();

// Clear session cookie
if (isset($_COOKIE[session_name()])) {
    setcookie(session_name(), '', time() - 3600, '/');
}

// Redirect to login page with success message
header('Location: login.php?message=logged_out');
exit();
?>
