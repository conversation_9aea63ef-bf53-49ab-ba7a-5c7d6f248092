-- Initial Data for AI-Powered LMS
-- Ogbonnaya Onu Polytechnic Departments and Setup Data

USE lms_ogbonnaya_onu;

-- Insert Academic Levels
INSERT INTO academic_levels (level_name, level_code, description) VALUES
('ND1', 'ND1', 'National Diploma Year 1'),
('ND2', 'ND2', 'National Diploma Year 2'),
('HND1', 'HND1', 'Higher National Diploma Year 1'),
('HND2', 'HND2', 'Higher National Diploma Year 2');

-- Insert Departments (31 courses)
INSERT INTO departments (name, code, description) VALUES
('Computer Science', 'CS', 'Computer Science and Information Technology'),
('Civil Engineering', 'CE', 'Civil and Structural Engineering'),
('Mass Communication', 'MC', 'Mass Communication and Media Studies'),
('Electrical/Electronic Engineering', 'EEE', 'Electrical and Electronic Engineering'),
('Mechanical Engineering', 'ME', 'Mechanical Engineering Technology'),
('Accountancy', 'ACC', 'Accounting and Financial Management'),
('Business Administration', 'BA', 'Business Administration and Management'),
('Banking and Finance', 'BF', 'Banking and Financial Services'),
('Marketing', 'MKT', 'Marketing and Sales Management'),
('Public Administration', 'PA', 'Public Administration and Governance'),
('Office Technology and Management', 'OTM', 'Office Technology and Management'),
('Statistics', 'STAT', 'Statistics and Data Analysis'),
('Architecture', 'ARCH', 'Architectural Technology'),
('Building Technology', 'BT', 'Building Construction Technology'),
('Quantity Surveying', 'QS', 'Quantity Surveying and Cost Management'),
('Estate Management', 'EM', 'Estate Management and Valuation'),
('Urban and Regional Planning', 'URP', 'Urban and Regional Planning'),
('Surveying and Geo-informatics', 'SGI', 'Surveying and Geo-informatics'),
('Agricultural Technology', 'AT', 'Agricultural Technology and Management'),
('Food Technology', 'FT', 'Food Science and Technology'),
('Hospitality Management', 'HM', 'Hospitality and Tourism Management'),
('Fashion Design and Clothing Technology', 'FDCT', 'Fashion Design and Clothing Technology'),
('Fine and Applied Arts', 'FAA', 'Fine and Applied Arts'),
('Music Technology', 'MT', 'Music Technology and Production'),
('Library and Information Science', 'LIS', 'Library and Information Science'),
('Science Laboratory Technology', 'SLT', 'Science Laboratory Technology'),
('Computer Engineering', 'CPE', 'Computer Engineering Technology'),
('Pharmaceutical Technology', 'PT', 'Pharmaceutical Technology'),
('Environmental Technology', 'ET', 'Environmental Technology and Management'),
('Cooperative Economics and Management', 'CEM', 'Cooperative Economics and Management'),
('Insurance', 'INS', 'Insurance and Risk Management');

-- Insert Security Questions
INSERT INTO security_questions (question) VALUES
('What is your mother\'s maiden name?'),
('What was the name of your first pet?'),
('In which city were you born?'),
('What is your favorite color?'),
('What was the name of your primary school?'),
('What is your favorite food?'),
('What was your childhood nickname?'),
('What is the name of your best friend?'),
('What was your first car model?'),
('What is your favorite movie?');

-- Insert Reward Types
INSERT INTO reward_types (name, description, icon, points_required, badge_image) VALUES
('First Quiz', 'Completed your first quiz', '🎯', 0, 'badge-first-quiz.png'),
('Perfect Score', 'Achieved 100% in a quiz', '⭐', 0, 'badge-perfect.png'),
('Quick Learner', 'Completed quiz in under 5 minutes', '⚡', 0, 'badge-speed.png'),
('Consistent Learner', '5 days streak', '🔥', 0, 'badge-streak-5.png'),
('Dedicated Student', '10 days streak', '💎', 0, 'badge-streak-10.png'),
('Quiz Master', 'Completed 10 quizzes', '👑', 100, 'badge-master.png'),
('Subject Expert', 'Achieved 90%+ average in a subject', '🏆', 200, 'badge-expert.png'),
('Knowledge Seeker', 'Attempted 50 questions', '📚', 50, 'badge-seeker.png'),
('Rising Star', 'Earned 500 points', '🌟', 500, 'badge-star.png'),
('Academic Champion', 'Earned 1000 points', '🏅', 1000, 'badge-champion.png');

-- Insert System Settings
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('quiz_time_limit', '1800', 'Default quiz time limit in seconds (30 minutes)'),
('questions_per_quiz', '20', 'Default number of questions per quiz'),
('points_per_correct', '10', 'Points awarded for each correct answer'),
('points_bonus_perfect', '50', 'Bonus points for perfect score'),
('points_bonus_speed', '25', 'Bonus points for completing quiz quickly'),
('streak_bonus_multiplier', '1.5', 'Multiplier for streak bonuses'),
('min_pass_percentage', '60', 'Minimum percentage to pass a quiz'),
('max_daily_quizzes', '5', 'Maximum quizzes a student can take per day'),
('school_name', 'Ogbonnaya Onu Polytechnic, Aba', 'Official school name'),
('school_motto', 'Excellence in Technical Education', 'School motto'),
('academic_session', '2024/2025', 'Current academic session'),
('registration_open', '1', 'Whether student registration is open (1=yes, 0=no)');

-- Insert Sample Subjects for Computer Science Department
INSERT INTO subjects (name, code, department_id, academic_level_id, description) VALUES
-- Computer Science ND1 Subjects
('Introduction to Computer Science', 'CS101', 1, 1, 'Basic concepts of computer science'),
('Computer Programming I', 'CS102', 1, 1, 'Introduction to programming concepts'),
('Mathematics for Computer Science', 'CS103', 1, 1, 'Mathematical foundations'),
('Computer Hardware', 'CS104', 1, 1, 'Computer hardware components and architecture'),
('Digital Logic Design', 'CS105', 1, 1, 'Logic gates and digital circuits'),

-- Computer Science ND2 Subjects
('Data Structures and Algorithms', 'CS201', 1, 2, 'Advanced data structures and algorithms'),
('Database Management Systems', 'CS202', 1, 2, 'Database design and management'),
('Web Development', 'CS203', 1, 2, 'HTML, CSS, JavaScript, and web technologies'),
('Operating Systems', 'CS204', 1, 2, 'Operating system concepts and design'),
('Software Engineering', 'CS205', 1, 2, 'Software development methodologies'),

-- Computer Science HND1 Subjects
('Advanced Programming', 'CS301', 1, 3, 'Advanced programming concepts and techniques'),
('Computer Networks', 'CS302', 1, 3, 'Network protocols and architecture'),
('Artificial Intelligence', 'CS303', 1, 3, 'AI concepts and machine learning basics'),
('Mobile App Development', 'CS304', 1, 3, 'Mobile application development'),
('Cybersecurity Fundamentals', 'CS305', 1, 3, 'Information security principles'),

-- Computer Science HND2 Subjects
('Machine Learning', 'CS401', 1, 4, 'Advanced machine learning algorithms'),
('Cloud Computing', 'CS402', 1, 4, 'Cloud platforms and services'),
('Project Management', 'CS403', 1, 4, 'IT project management methodologies'),
('Advanced Database Systems', 'CS404', 1, 4, 'Advanced database concepts'),
('Capstone Project', 'CS405', 1, 4, 'Final year project');

-- Insert Sample Subjects for Civil Engineering Department
INSERT INTO subjects (name, code, department_id, academic_level_id, description) VALUES
-- Civil Engineering ND1 Subjects
('Engineering Mathematics I', 'CE101', 2, 1, 'Mathematical foundations for engineering'),
('Engineering Drawing', 'CE102', 2, 1, 'Technical drawing and CAD basics'),
('Strength of Materials', 'CE103', 2, 1, 'Material properties and stress analysis'),
('Surveying I', 'CE104', 2, 1, 'Basic surveying techniques'),
('Construction Technology I', 'CE105', 2, 1, 'Basic construction methods'),

-- Civil Engineering ND2 Subjects
('Structural Analysis', 'CE201', 2, 2, 'Analysis of structures and loads'),
('Concrete Technology', 'CE202', 2, 2, 'Concrete mix design and properties'),
('Hydraulics', 'CE203', 2, 2, 'Fluid mechanics and hydraulic systems'),
('Soil Mechanics', 'CE204', 2, 2, 'Soil properties and foundation design'),
('Highway Engineering', 'CE205', 2, 2, 'Road design and construction'),

-- Civil Engineering HND1 Subjects
('Advanced Structural Design', 'CE301', 2, 3, 'Advanced structural analysis and design'),
('Environmental Engineering', 'CE302', 2, 3, 'Water and wastewater treatment'),
('Construction Management', 'CE303', 2, 3, 'Project planning and management'),
('Geotechnical Engineering', 'CE304', 2, 3, 'Advanced soil mechanics'),
('Transportation Engineering', 'CE305', 2, 3, 'Transportation systems design'),

-- Civil Engineering HND2 Subjects
('Advanced Concrete Design', 'CE401', 2, 4, 'Reinforced and prestressed concrete'),
('Steel Structure Design', 'CE402', 2, 4, 'Steel structure analysis and design'),
('Water Resources Engineering', 'CE403', 2, 4, 'Water resource management'),
('Construction Economics', 'CE404', 2, 4, 'Cost estimation and economics'),
('Final Year Project', 'CE405', 2, 4, 'Capstone engineering project');
