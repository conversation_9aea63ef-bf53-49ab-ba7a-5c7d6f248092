<?php
/**
 * Students Management for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Require admin login
requireLogin('admin');

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'approve':
                case 'approve_student':
                    $id = isset($_POST['id']) ? (int)$_POST['id'] : (int)$_POST['student_id'];
                    executeQuery("UPDATE students SET is_approved = 1 WHERE id = :id", ['id' => $id]);
                    $success = "Student approved successfully!";
                    break;

                case 'reject':
                case 'reject_student':
                    $id = isset($_POST['id']) ? (int)$_POST['id'] : (int)$_POST['student_id'];
                    executeQuery("UPDATE students SET is_approved = 0 WHERE id = :id", ['id' => $id]);
                    $success = "Student approval revoked!";
                    break;

                case 'delete':
                case 'delete_student':
                    $id = isset($_POST['id']) ? (int)$_POST['id'] : (int)$_POST['student_id'];

                    // Check if student has quiz sessions
                    $quizCount = fetchOne("SELECT COUNT(*) as count FROM quiz_sessions WHERE student_id = :id", ['id' => $id])['count'];
                    if ($quizCount > 0) {
                        throw new Exception("Cannot delete student with existing quiz sessions. Please archive instead.");
                    }

                    executeQuery("DELETE FROM students WHERE id = :id", ['id' => $id]);
                    $success = "Student deleted successfully!";
                    break;

                case 'reset_password':
                    $id = (int)$_POST['id'];
                    $newPassword = 'student123'; // Default password
                    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

                    executeQuery("UPDATE students SET password_hash = :password, updated_at = NOW() WHERE id = :id", [
                        'id' => $id,
                        'password' => $hashedPassword
                    ]);
                    
                    $success = "Password reset to 'student123' successfully!";
                    break;
            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get filter parameters
$status = $_GET['status'] ?? 'all';
$department = $_GET['department'] ?? '';
$level = $_GET['level'] ?? '';
$search = $_GET['search'] ?? '';

// Build query conditions
$conditions = [];
$params = [];

if ($status === 'approved') {
    $conditions[] = "s.is_approved = 1";
} elseif ($status === 'pending') {
    $conditions[] = "s.is_approved = 0";
}

if ($department) {
    $conditions[] = "s.department_id = :department";
    $params['department'] = $department;
}

if ($level) {
    $conditions[] = "s.academic_level_id = :level";
    $params['level'] = $level;
}

if ($search) {
    $conditions[] = "(s.first_name LIKE :search OR s.last_name LIKE :search OR s.student_id LIKE :search OR s.email LIKE :search)";
    $params['search'] = "%$search%";
}

$whereClause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';

// Get students with department and level info
try {
    $students = fetchAll("
        SELECT s.*, d.name as department_name, al.level_name,
               (SELECT COUNT(*) FROM quiz_sessions WHERE student_id = s.id AND status = 'completed') as quiz_count,
               (SELECT AVG(score_percentage) FROM quiz_sessions WHERE student_id = s.id AND status = 'completed') as avg_score
        FROM students s
        JOIN departments d ON s.department_id = d.id
        JOIN academic_levels al ON s.academic_level_id = al.id
        $whereClause
        ORDER BY s.registration_date DESC
    ", $params);
} catch (Exception $e) {
    // If there's an error with the complex query, try a simpler one
    $students = fetchAll("
        SELECT s.*, d.name as department_name, al.level_name,
               0 as quiz_count, 0 as avg_score
        FROM students s
        JOIN departments d ON s.department_id = d.id
        JOIN academic_levels al ON s.academic_level_id = al.id
        $whereClause
        ORDER BY s.registration_date DESC
    ", $params);
}

// Get departments and levels for filters
$departments = fetchAll("SELECT * FROM departments ORDER BY name");
$academicLevels = fetchAll("SELECT * FROM academic_levels ORDER BY level_name");

// Get statistics
$totalStudents = fetchOne("SELECT COUNT(*) as count FROM students")['count'];
$approvedStudents = fetchOne("SELECT COUNT(*) as count FROM students WHERE is_approved = 1")['count'];
$pendingStudents = fetchOne("SELECT COUNT(*) as count FROM students WHERE is_approved = 0")['count'];

// Debug information (remove in production)
$debugInfo = [
    'total_students' => $totalStudents,
    'approved_students' => $approvedStudents,
    'pending_students' => $pendingStudents,
    'filter_status' => $status,
    'students_found' => count($students),
    'where_clause' => $whereClause
];

$pageTitle = 'Students Management';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Admin Panel</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="students.php" class="nav-item active">
                    <i class="fas fa-users"></i>
                    <span>Students</span>
                    <?php if ($pendingStudents > 0): ?>
                        <span class="badge"><?php echo $pendingStudents; ?></span>
                    <?php endif; ?>
                </a>
                <a href="departments.php" class="nav-item">
                    <i class="fas fa-building"></i>
                    <span>Departments</span>
                </a>
                <a href="questions.php" class="nav-item">
                    <i class="fas fa-robot"></i>
                    <span>AI Questions</span>
                </a>
                <a href="rewards.php" class="nav-item">
                    <i class="fas fa-trophy"></i>
                    <span>Rewards</span>
                </a>
                <a href="analytics.php" class="nav-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>Analytics</span>
                </a>
                <a href="settings.php" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>

                <div class="nav-divider"></div>

                <a href="../index.php" class="nav-item nav-secondary">
                    <i class="fas fa-home"></i>
                    <span>Back to Site</span>
                </a>
                <a href="../logout.php" class="nav-item nav-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>
            
            <div class="sidebar-footer">
                <a href="logout.php" class="nav-item logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="content-header">
                <div class="header-left">
                    <h1>Students Management</h1>
                    <p>Manage student registrations, approvals, and monitor progress</p>
                </div>
                <div class="header-right">
                    <div class="header-stats">
                        <div class="stat-item">
                            <span class="stat-number"><?php echo $totalStudents; ?></span>
                            <span class="stat-label">Total</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo $approvedStudents; ?></span>
                            <span class="stat-label">Approved</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo $pendingStudents; ?></span>
                            <span class="stat-label">Pending</span>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Messages -->
            <?php if (isset($success)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <!-- Filters -->
            <div class="filters-section">
                <form method="GET" class="filters-form">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" name="search" placeholder="Search students..." value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    
                    <div class="filter-controls">
                        <select name="status">
                            <option value="all" <?php echo $status === 'all' ? 'selected' : ''; ?>>All Status</option>
                            <option value="approved" <?php echo $status === 'approved' ? 'selected' : ''; ?>>Approved</option>
                            <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                        </select>
                        
                        <select name="department">
                            <option value="">All Departments</option>
                            <?php foreach ($departments as $dept): ?>
                                <option value="<?php echo $dept['id']; ?>" <?php echo $department == $dept['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($dept['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        
                        <select name="level">
                            <option value="">All Levels</option>
                            <?php foreach ($academicLevels as $level_item): ?>
                                <option value="<?php echo $level_item['id']; ?>" <?php echo $level == $level_item['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($level_item['level_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i>
                            Filter
                        </button>
                        
                        <a href="students.php" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            Clear
                        </a>
                    </div>
                </form>
            </div>
            
            <!-- Students List -->
            <div class="students-container">
                <?php if (empty($students)): ?>
                    <div class="empty-state">
                        <i class="fas fa-users"></i>
                        <h3>No Students Found</h3>
                        <p>No students match your current filters</p>

                        <?php if ($totalStudents > 0): ?>
                            <div class="debug-info" style="background: #f8fafc; padding: 1rem; border-radius: 8px; margin: 1rem 0; text-align: left;">
                                <h4>Debug Information:</h4>
                                <ul>
                                    <li>Total students in database: <?php echo $totalStudents; ?></li>
                                    <li>Approved students: <?php echo $approvedStudents; ?></li>
                                    <li>Pending students: <?php echo $pendingStudents; ?></li>
                                    <li>Current filter: <?php echo $status; ?></li>
                                    <li>Students found with current filter: <?php echo count($students); ?></li>
                                </ul>
                                <p><strong>Suggestion:</strong> Try changing the filter to "All Status" or "Pending" to see if students appear.</p>
                            </div>
                        <?php endif; ?>

                        <div class="empty-state-actions">
                            <a href="../student/register.php" class="btn btn-primary">
                                <i class="fas fa-user-plus"></i>
                                Register New Student
                            </a>
                            <?php if ($totalStudents > 0): ?>
                                <a href="?status=all" class="btn btn-secondary">
                                    <i class="fas fa-filter"></i>
                                    Show All Students
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="students-grid">
                        <div class="students-header">
                            <h3>Students List (<?php echo count($students); ?> found)</h3>
                            <div class="view-toggle">
                                <button class="btn-toggle active" data-view="grid">
                                    <i class="fas fa-th-large"></i>
                                </button>
                                <button class="btn-toggle" data-view="table">
                                    <i class="fas fa-list"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Grid View -->
                        <div class="students-grid-view active">
                            <div class="students-cards">
                                <?php foreach ($students as $student): ?>
                                    <div class="student-card">
                                        <div class="student-card-header">
                                            <div class="student-avatar">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div class="student-basic-info">
                                                <h4><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h4>
                                                <p class="student-id"><?php echo htmlspecialchars($student['student_id']); ?></p>
                                                <p class="student-email"><?php echo htmlspecialchars($student['email']); ?></p>
                                            </div>
                                            <div class="student-status">
                                                <?php if ($student['is_approved']): ?>
                                                    <span class="status approved">
                                                        <i class="fas fa-check-circle"></i>
                                                        Approved
                                                    </span>
                                                <?php else: ?>
                                                    <span class="status pending">
                                                        <i class="fas fa-clock"></i>
                                                        Pending Approval
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <div class="student-card-body">
                                            <div class="student-details-grid">
                                                <div class="detail-item">
                                                    <span class="detail-label">Department</span>
                                                    <span class="detail-value"><?php echo htmlspecialchars($student['department_name']); ?></span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-label">Level</span>
                                                    <span class="detail-value"><?php echo htmlspecialchars($student['level_name']); ?></span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-label">Quizzes Taken</span>
                                                    <span class="detail-value"><?php echo $student['quiz_count']; ?></span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-label">Average Score</span>
                                                    <span class="detail-value">
                                                        <?php if ($student['avg_score']): ?>
                                                            <span class="score-badge <?php echo $student['avg_score'] >= 70 ? 'good' : ($student['avg_score'] >= 50 ? 'average' : 'poor'); ?>">
                                                                <?php echo round($student['avg_score']); ?>%
                                                            </span>
                                                        <?php else: ?>
                                                            <span class="no-data">No quizzes yet</span>
                                                        <?php endif; ?>
                                                    </span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-label">Joined</span>
                                                    <span class="detail-value"><?php echo date('M d, Y', strtotime($student['registration_date'])); ?></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="student-card-actions">
                                            <button class="btn-action primary" onclick="viewStudentProgress(<?php echo $student['id']; ?>)" title="View Progress">
                                                <i class="fas fa-chart-line"></i>
                                                Progress
                                            </button>

                                            <?php if (!$student['is_approved']): ?>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="approve_student">
                                                    <input type="hidden" name="id" value="<?php echo $student['id']; ?>">
                                                    <button type="submit" class="btn-action success" title="Approve Student">
                                                        <i class="fas fa-check"></i>
                                                        Approve
                                                    </button>
                                                </form>
                                            <?php else: ?>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="reject_student">
                                                    <input type="hidden" name="id" value="<?php echo $student['id']; ?>">
                                                    <button type="submit" class="btn-action warning" title="Revoke Approval">
                                                        <i class="fas fa-times"></i>
                                                        Revoke
                                                    </button>
                                                </form>
                                            <?php endif; ?>

                                            <button class="btn-action secondary" onclick="resetPassword(<?php echo $student['id']; ?>)" title="Reset Password">
                                                <i class="fas fa-key"></i>
                                                Reset
                                            </button>

                                            <button class="btn-action danger" onclick="deleteStudent(<?php echo $student['id']; ?>, '<?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>')" title="Delete Student">
                                                <i class="fas fa-trash"></i>
                                                Delete
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Table View -->
                        <div class="students-table-view">
                            <div class="data-table">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>Student</th>
                                            <th>Department</th>
                                            <th>Level</th>
                                            <th>Status</th>
                                            <th>Quizzes</th>
                                            <th>Avg Score</th>
                                            <th>Joined</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($students as $student): ?>
                                            <tr>
                                                <td>
                                                    <div class="student-info">
                                                        <div class="student-avatar">
                                                            <i class="fas fa-user"></i>
                                                        </div>
                                                        <div class="student-details">
                                                            <strong><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></strong>
                                                            <small><?php echo htmlspecialchars($student['student_id']); ?></small>
                                                            <small><?php echo htmlspecialchars($student['email']); ?></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><?php echo htmlspecialchars($student['department_name']); ?></td>
                                                <td><?php echo htmlspecialchars($student['level_name']); ?></td>
                                                <td>
                                                    <?php if ($student['is_approved']): ?>
                                                        <span class="status approved">
                                                            <i class="fas fa-check-circle"></i>
                                                            Approved
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="status pending">
                                                            <i class="fas fa-clock"></i>
                                                            Pending
                                                        </span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo $student['quiz_count']; ?></td>
                                                <td>
                                                    <?php if ($student['avg_score']): ?>
                                                        <span class="score-badge <?php echo $student['avg_score'] >= 70 ? 'good' : ($student['avg_score'] >= 50 ? 'average' : 'poor'); ?>">
                                                            <?php echo round($student['avg_score']); ?>%
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="no-data">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('M d, Y', strtotime($student['registration_date'])); ?></td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-icon" onclick="viewStudentProgress(<?php echo $student['id']; ?>)" title="View Progress">
                                                            <i class="fas fa-chart-line"></i>
                                                        </button>

                                                        <?php if (!$student['is_approved']): ?>
                                                            <form method="POST" style="display: inline;">
                                                                <input type="hidden" name="action" value="approve_student">
                                                                <input type="hidden" name="id" value="<?php echo $student['id']; ?>">
                                                                <button type="submit" class="btn-icon approve" title="Approve Student">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                            </form>
                                                        <?php else: ?>
                                                            <form method="POST" style="display: inline;">
                                                                <input type="hidden" name="action" value="reject_student">
                                                                <input type="hidden" name="id" value="<?php echo $student['id']; ?>">
                                                                <button type="submit" class="btn-icon reject" title="Revoke Approval">
                                                                    <i class="fas fa-times"></i>
                                                                </button>
                                                            </form>
                                                        <?php endif; ?>

                                                        <button class="btn-icon" onclick="resetPassword(<?php echo $student['id']; ?>)" title="Reset Password">
                                                            <i class="fas fa-key"></i>
                                                        </button>

                                                        <button class="btn-icon delete" onclick="deleteStudent(<?php echo $student['id']; ?>, '<?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>')" title="Delete Student">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
    
    <script src="../assets/js/admin-dashboard.js"></script>
    <script src="../assets/js/students.js"></script>
</body>
</html>
