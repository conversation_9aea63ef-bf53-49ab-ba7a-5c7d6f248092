<?php
/**
 * Delete Question AJAX Endpoint
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../../config/database.php';

// Set JSON header
header('Content-Type: application/json');

try {
    // Require admin login
    requireLogin('admin');
    
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST requests are allowed');
    }
    
    // Get question ID
    $questionId = (int)($_POST['id'] ?? 0);
    
    if (!$questionId) {
        throw new Exception('Question ID is required');
    }
    
    // Check if question exists
    $question = fetchOne("
        SELECT q.*, s.name as subject_name
        FROM questions q
        JOIN subjects s ON q.subject_id = s.id
        WHERE q.id = :id
    ", ['id' => $questionId]);
    
    if (!$question) {
        throw new Exception('Question not found');
    }
    
    // Check if question has been used in quizzes
    $usageCount = fetchOne("
        SELECT COUNT(*) as count
        FROM quiz_answers
        WHERE question_id = :id
    ", ['id' => $questionId])['count'];
    
    if ($usageCount > 0) {
        // Don't actually delete if it has been used, just mark as inactive
        execute("
            UPDATE questions 
            SET is_active = 0, updated_at = NOW()
            WHERE id = :id
        ", ['id' => $questionId]);
        
        $message = "Question has been deactivated (it was used in {$usageCount} quiz attempts)";
    } else {
        // Safe to delete completely
        execute("DELETE FROM questions WHERE id = :id", ['id' => $questionId]);
        $message = "Question deleted successfully";
    }
    
    // Log the action
    $logData = [
        'admin_id' => $_SESSION['admin_id'] ?? 0,
        'action' => $usageCount > 0 ? 'deactivate_question' : 'delete_question',
        'question_id' => $questionId,
        'question_text' => $question['question_text'],
        'subject_name' => $question['subject_name'],
        'usage_count' => $usageCount,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    // You could log this to a separate admin_logs table if you have one
    // execute("INSERT INTO admin_logs (admin_id, action, details, created_at) VALUES (:admin_id, :action, :details, NOW())", [
    //     'admin_id' => $logData['admin_id'],
    //     'action' => $logData['action'],
    //     'details' => json_encode($logData)
    // ]);
    
    // Response data
    $response = [
        'success' => true,
        'message' => $message,
        'question_id' => $questionId,
        'was_deactivated' => $usageCount > 0,
        'usage_count' => (int)$usageCount,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
