<?php
/**
 * Fix Database Schema for Student Quiz System
 * Creates missing tables and updates existing ones
 */

require_once '../config/database.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Fix Database Schema</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; padding: 10px; background: #d4edda; border-radius: 5px; margin: 10px 0; }
        .error { color: red; padding: 10px; background: #f8d7da; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; padding: 10px; background: #d1ecf1; border-radius: 5px; margin: 10px 0; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>";

echo "<h1>🔧 Database Schema Fix for Student Quiz System</h1>";

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='step'>";
    echo "<h2>📋 Step 1: Create quiz_questions table</h2>";
    
    // Create quiz_questions table if it doesn't exist
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS quiz_questions (
                id INT PRIMARY KEY AUTO_INCREMENT,
                quiz_session_id INT NOT NULL,
                question_id INT NOT NULL,
                question_number INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (quiz_session_id) REFERENCES quiz_sessions(id) ON DELETE CASCADE,
                FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
                UNIQUE KEY unique_quiz_question (quiz_session_id, question_number)
            )
        ");
        echo "<div class='success'>✅ quiz_questions table created successfully</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error creating quiz_questions table: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>📝 Step 2: Create quiz_answers table</h2>";
    
    // Create quiz_answers table if it doesn't exist
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS quiz_answers (
                id INT PRIMARY KEY AUTO_INCREMENT,
                quiz_session_id INT NOT NULL,
                question_id INT NOT NULL,
                question_number INT NOT NULL,
                selected_answer ENUM('A', 'B', 'C', 'D') NOT NULL,
                is_correct BOOLEAN DEFAULT FALSE,
                answered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (quiz_session_id) REFERENCES quiz_sessions(id) ON DELETE CASCADE,
                FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
                UNIQUE KEY unique_quiz_answer (quiz_session_id, question_number)
            )
        ");
        echo "<div class='success'>✅ quiz_answers table created successfully</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error creating quiz_answers table: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>🔄 Step 3: Update quiz_sessions table structure</h2>";
    
    // Add missing columns to quiz_sessions table
    try {
        // Check if columns exist first
        $columns = $pdo->query("DESCRIBE quiz_sessions")->fetchAll(PDO::FETCH_COLUMN);
        
        if (!in_array('department_id', $columns)) {
            $pdo->exec("ALTER TABLE quiz_sessions ADD COLUMN department_id INT NULL AFTER student_id");
            echo "<div class='success'>✅ Added department_id column to quiz_sessions</div>";
        } else {
            echo "<div class='info'>ℹ️ department_id column already exists in quiz_sessions</div>";
        }
        
        if (!in_array('academic_level_id', $columns)) {
            $pdo->exec("ALTER TABLE quiz_sessions ADD COLUMN academic_level_id INT NULL AFTER department_id");
            echo "<div class='success'>✅ Added academic_level_id column to quiz_sessions</div>";
        } else {
            echo "<div class='info'>ℹ️ academic_level_id column already exists in quiz_sessions</div>";
        }
        
        if (!in_array('score', $columns)) {
            $pdo->exec("ALTER TABLE quiz_sessions ADD COLUMN score DECIMAL(5,2) DEFAULT 0.00 AFTER total_questions");
            echo "<div class='success'>✅ Added score column to quiz_sessions</div>";
        } else {
            echo "<div class='info'>ℹ️ score column already exists in quiz_sessions</div>";
        }
        
        if (!in_array('time_taken', $columns)) {
            $pdo->exec("ALTER TABLE quiz_sessions ADD COLUMN time_taken INT DEFAULT 0 AFTER score");
            echo "<div class='success'>✅ Added time_taken column to quiz_sessions</div>";
        } else {
            echo "<div class='info'>ℹ️ time_taken column already exists in quiz_sessions</div>";
        }
        
        if (!in_array('started_at', $columns)) {
            $pdo->exec("ALTER TABLE quiz_sessions ADD COLUMN started_at TIMESTAMP NULL AFTER time_taken");
            echo "<div class='success'>✅ Added started_at column to quiz_sessions</div>";
        } else {
            echo "<div class='info'>ℹ️ started_at column already exists in quiz_sessions</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error updating quiz_sessions table: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>📚 Step 4: Update questions table structure</h2>";
    
    // Add missing columns to questions table
    try {
        $columns = $pdo->query("DESCRIBE questions")->fetchAll(PDO::FETCH_COLUMN);
        
        if (!in_array('department_id', $columns)) {
            $pdo->exec("ALTER TABLE questions ADD COLUMN department_id INT NULL AFTER id");
            echo "<div class='success'>✅ Added department_id column to questions</div>";
        } else {
            echo "<div class='info'>ℹ️ department_id column already exists in questions</div>";
        }
        
        if (!in_array('academic_level_id', $columns)) {
            $pdo->exec("ALTER TABLE questions ADD COLUMN academic_level_id INT NULL AFTER department_id");
            echo "<div class='success'>✅ Added academic_level_id column to questions</div>";
        } else {
            echo "<div class='info'>ℹ️ academic_level_id column already exists in questions</div>";
        }
        
        if (!in_array('difficulty', $columns)) {
            $pdo->exec("ALTER TABLE questions ADD COLUMN difficulty ENUM('easy', 'medium', 'hard') DEFAULT 'medium' AFTER explanation");
            echo "<div class='success'>✅ Added difficulty column to questions</div>";
        } else {
            echo "<div class='info'>ℹ️ difficulty column already exists in questions</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error updating questions table: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>🗂️ Step 5: Create activity_logs table</h2>";
    
    // Create activity_logs table if it doesn't exist
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                user_type ENUM('student', 'admin') NOT NULL,
                action VARCHAR(100) NOT NULL,
                details TEXT,
                ip_address VARCHAR(45),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");
        echo "<div class='success'>✅ activity_logs table created successfully</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error creating activity_logs table: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>🧹 Step 6: Clean up old subject references</h2>";
    
    // Remove subject_id from quiz_sessions if it exists
    try {
        $columns = $pdo->query("DESCRIBE quiz_sessions")->fetchAll(PDO::FETCH_COLUMN);
        
        if (in_array('subject_id', $columns)) {
            $pdo->exec("ALTER TABLE quiz_sessions DROP COLUMN subject_id");
            echo "<div class='success'>✅ Removed subject_id column from quiz_sessions</div>";
        } else {
            echo "<div class='info'>ℹ️ subject_id column doesn't exist in quiz_sessions</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error removing subject_id: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>✅ Step 7: Verification</h2>";
    
    // Verify all tables exist
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    $requiredTables = ['quiz_questions', 'quiz_answers', 'quiz_sessions', 'questions', 'activity_logs'];
    
    foreach ($requiredTables as $table) {
        if (in_array($table, $tables)) {
            echo "<div class='success'>✅ Table '$table' exists</div>";
        } else {
            echo "<div class='error'>❌ Table '$table' is missing</div>";
        }
    }
    echo "</div>";
    
    echo "<div class='success'><h2>🎉 Database schema fix completed!</h2>";
    echo "<p>You can now test the student quiz system. <a href='quiz.php'>Go to Quiz Page</a></p></div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database connection error: " . $e->getMessage() . "</div>";
}

echo "</body></html>";
?>
