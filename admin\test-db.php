<?php
/**
 * Database Test for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

echo "<h2>Database Connection Test</h2>";

try {
    // Test basic connection
    $db = new Database();
    $conn = $db->getConnection();
    
    if ($conn) {
        echo "<p style='color: green;'>✓ Database connection successful!</p>";
        
        // Test departments table
        try {
            $departments = fetchAll("SELECT * FROM departments LIMIT 5");
            echo "<p style='color: green;'>✓ Departments table exists and accessible</p>";
            echo "<p>Found " . count($departments) . " departments</p>";
            
            if (count($departments) > 0) {
                echo "<h3>Sample Departments:</h3>";
                foreach ($departments as $dept) {
                    echo "<li>" . htmlspecialchars($dept['name']) . " (" . htmlspecialchars($dept['code']) . ")</li>";
                }
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Error accessing departments table: " . $e->getMessage() . "</p>";
        }
        
        // Test departments table
        try {
            $departments = fetchAll("SELECT * FROM departments LIMIT 5");
            echo "<p style='color: green;'>✓ Departments table exists and accessible</p>";
            echo "<p>Found " . count($departments) . " departments</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Error accessing departments table: " . $e->getMessage() . "</p>";
        }
        
        // Test questions table
        try {
            $questions = fetchAll("SELECT * FROM questions LIMIT 5");
            echo "<p style='color: green;'>✓ Questions table exists and accessible</p>";
            echo "<p>Found " . count($questions) . " questions</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Error accessing questions table: " . $e->getMessage() . "</p>";
        }
        
        // Test students table
        try {
            $students = fetchAll("SELECT * FROM students LIMIT 5");
            echo "<p style='color: green;'>✓ Students table exists and accessible</p>";
            echo "<p>Found " . count($students) . " students</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Error accessing students table: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Database connection failed!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
}

echo "<br><a href='dashboard.php'>← Back to Dashboard</a>";
?>
