<?php
/**
 * Subjects Management for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Require admin login
requireLogin('admin');

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add_subject':
                    $name = trim($_POST['name']);
                    $code = trim($_POST['code']);
                    $department_id = (int)$_POST['department_id'];
                    $academic_level_id = (int)$_POST['academic_level_id'];
                    $description = trim($_POST['description']);
                    
                    if (empty($name) || empty($code)) {
                        throw new Exception('Subject name and code are required');
                    }
                    
                    // Check if subject code already exists
                    $existing = fetchOne("SELECT id FROM subjects WHERE code = :code", ['code' => $code]);
                    if ($existing) {
                        throw new Exception('Subject code already exists');
                    }
                    
                    execute("
                        INSERT INTO subjects (name, code, department_id, academic_level_id, description, created_at) 
                        VALUES (:name, :code, :department_id, :academic_level_id, :description, NOW())
                    ", [
                        'name' => $name,
                        'code' => $code,
                        'department_id' => $department_id,
                        'academic_level_id' => $academic_level_id,
                        'description' => $description
                    ]);
                    
                    $success = "Subject added successfully!";
                    break;
                    
                case 'edit_subject':
                    $id = (int)$_POST['id'];
                    $name = trim($_POST['name']);
                    $code = trim($_POST['code']);
                    $department_id = (int)$_POST['department_id'];
                    $academic_level_id = (int)$_POST['academic_level_id'];
                    $description = trim($_POST['description']);
                    
                    if (empty($name) || empty($code)) {
                        throw new Exception('Subject name and code are required');
                    }
                    
                    // Check if subject code already exists (excluding current subject)
                    $existing = fetchOne("SELECT id FROM subjects WHERE code = :code AND id != :id", ['code' => $code, 'id' => $id]);
                    if ($existing) {
                        throw new Exception('Subject code already exists');
                    }
                    
                    execute("
                        UPDATE subjects 
                        SET name = :name, code = :code, department_id = :department_id, 
                            academic_level_id = :academic_level_id, description = :description, updated_at = NOW()
                        WHERE id = :id
                    ", [
                        'id' => $id,
                        'name' => $name,
                        'code' => $code,
                        'department_id' => $department_id,
                        'academic_level_id' => $academic_level_id,
                        'description' => $description
                    ]);
                    
                    $success = "Subject updated successfully!";
                    break;
                    
                case 'delete_subject':
                    $id = (int)$_POST['id'];
                    
                    // Check if subject has questions
                    $questionCount = fetchOne("SELECT COUNT(*) as count FROM questions WHERE subject_id = :id", ['id' => $id])['count'];
                    if ($questionCount > 0) {
                        throw new Exception("Cannot delete subject with existing questions. Please remove all questions first.");
                    }
                    
                    execute("DELETE FROM subjects WHERE id = :id", ['id' => $id]);
                    $success = "Subject deleted successfully!";
                    break;
            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get subjects with department and level info
$subjects = fetchAll("
    SELECT s.*, d.name as department_name, al.level_name,
           (SELECT COUNT(*) FROM questions WHERE subject_id = s.id) as question_count
    FROM subjects s
    JOIN departments d ON s.department_id = d.id
    JOIN academic_levels al ON s.academic_level_id = al.id
    ORDER BY d.name, al.level_name, s.name
");

// Get departments and academic levels for forms
$departments = fetchAll("SELECT * FROM departments ORDER BY name");
$academicLevels = fetchAll("SELECT * FROM academic_levels ORDER BY level_name");

$pageTitle = 'Subjects Management';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Admin Panel</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="students.php" class="nav-item">
                    <i class="fas fa-users"></i>
                    <span>Students</span>
                </a>
                <a href="departments.php" class="nav-item">
                    <i class="fas fa-building"></i>
                    <span>Departments</span>
                </a>
                <a href="subjects.php" class="nav-item active">
                    <i class="fas fa-book"></i>
                    <span>Subjects</span>
                </a>
                <a href="questions.php" class="nav-item">
                    <i class="fas fa-question-circle"></i>
                    <span>Questions</span>
                </a>
                <a href="rewards.php" class="nav-item">
                    <i class="fas fa-trophy"></i>
                    <span>Rewards</span>
                </a>
                <a href="analytics.php" class="nav-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>Analytics</span>
                </a>
                <a href="settings.php" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>

                <div class="nav-divider"></div>

                <a href="../index.php" class="nav-item nav-secondary">
                    <i class="fas fa-home"></i>
                    <span>Back to Site</span>
                </a>
                <a href="../logout.php" class="nav-item nav-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>
            
            <div class="sidebar-footer">
                <a href="logout.php" class="nav-item logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="content-header">
                <div class="header-left">
                    <h1>Subjects Management</h1>
                    <p>Add, edit, and manage course subjects for different departments and levels</p>
                </div>
                <div class="header-right">
                    <button class="btn btn-primary" onclick="openAddSubjectModal()">
                        <i class="fas fa-plus"></i>
                        Add New Subject
                    </button>
                </div>
            </header>
            
            <!-- Messages -->
            <?php if (isset($success)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <!-- Subjects Grid -->
            <div class="subjects-container">
                <?php if (empty($subjects)): ?>
                    <div class="empty-state">
                        <i class="fas fa-book"></i>
                        <h3>No Subjects Found</h3>
                        <p>Start by adding your first course subject</p>
                        <button class="btn btn-primary" onclick="openAddSubjectModal()">
                            <i class="fas fa-plus"></i>
                            Add First Subject
                        </button>
                    </div>
                <?php else: ?>
                    <div class="subjects-grid">
                        <?php foreach ($subjects as $subject): ?>
                            <div class="subject-card">
                                <div class="subject-header">
                                    <div class="subject-icon">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    <div class="subject-actions">
                                        <button class="btn-icon" onclick="editSubject(<?php echo $subject['id']; ?>)" title="Edit Subject">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon delete" onclick="deleteSubject(<?php echo $subject['id']; ?>, '<?php echo htmlspecialchars($subject['name']); ?>')" title="Delete Subject">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="subject-content">
                                    <h3><?php echo htmlspecialchars($subject['name']); ?></h3>
                                    <p class="subject-code"><?php echo htmlspecialchars($subject['code']); ?></p>
                                    <p class="subject-dept"><?php echo htmlspecialchars($subject['department_name']); ?></p>
                                    <p class="subject-level"><?php echo htmlspecialchars($subject['level_name']); ?></p>
                                    
                                    <?php if ($subject['description']): ?>
                                        <p class="subject-description"><?php echo htmlspecialchars($subject['description']); ?></p>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="subject-footer">
                                    <div class="subject-stats">
                                        <span class="stat-item">
                                            <i class="fas fa-question-circle"></i>
                                            <?php echo $subject['question_count']; ?> questions
                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
    
    <!-- Add/Edit Subject Modal -->
    <div id="subjectModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Add New Subject</h3>
                <span class="close" onclick="closeSubjectModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="subjectForm" method="POST">
                    <input type="hidden" name="action" id="formAction" value="add_subject">
                    <input type="hidden" name="id" id="subjectId">
                    
                    <div class="form-group">
                        <label for="name">Subject Name *</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="code">Subject Code *</label>
                        <input type="text" id="code" name="code" required placeholder="e.g., MTH101">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="department_id">Department *</label>
                            <select id="department_id" name="department_id" required>
                                <option value="">Select Department</option>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?php echo $dept['id']; ?>"><?php echo htmlspecialchars($dept['name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="academic_level_id">Academic Level *</label>
                            <select id="academic_level_id" name="academic_level_id" required>
                                <option value="">Select Level</option>
                                <?php foreach ($academicLevels as $level): ?>
                                    <option value="<?php echo $level['id']; ?>"><?php echo htmlspecialchars($level['level_name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" rows="3" placeholder="Optional subject description"></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeSubjectModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            <span id="submitText">Add Subject</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="../assets/js/admin-dashboard.js"></script>
    <script src="../assets/js/subjects.js"></script>
</body>
</html>
