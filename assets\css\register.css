/* Registration-specific styles for AI-Powered LMS */

.auth-container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    min-height: 100vh;
    width: 100vw;
    background: none;
}

.register-card {
    max-width: 1200px;
    min-width: 600px;
    width: 100%;
    margin: 2rem auto;
}

.register-form {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    border-radius: 20px;
    padding: 2rem 2rem;
    border: 2px solid rgba(59, 130, 246, 0.15);
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.1);
    display: block;
    width: 100%;
    overflow-x: visible;
}

.form-section {
    margin-bottom: 2rem;
}

.form-section:last-child {
    margin-bottom: 0;
}

.form-section h3 {
    color: #1e40af;
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
    text-align: left;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid rgba(59, 130, 246, 0.1);
}

.form-section h3 i {
    color: #3b82f6;
    font-size: 1.3rem;
}

.section-description {
    color: #64748b;
    font-size: 0.9rem;
    margin-bottom: 16px;
    line-height: 1.4;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group:last-child {
    margin-bottom: 0;
}

/* Make single form groups also use 2-column layout when appropriate */
.form-section .form-group:not(.form-row .form-group) {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: start;
}

.form-section .form-group:not(.form-row .form-group) label {
    grid-column: 1 / -1;
    margin-bottom: 0.5rem;
}

.form-section .form-group:not(.form-row .form-group) input,
.form-section .form-group:not(.form-row .form-group) select {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    display: block;
    font-size: 0.95rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 16px 20px;
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-radius: 12px;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    font-family: inherit;
    backdrop-filter: blur(5px);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15);
    transform: translateY(-1px);
    background: white;
}

.form-group select {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
    appearance: none;
}

.form-help {
    display: block;
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 4px;
    line-height: 1.4;
}

.security-question-group {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 16px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.security-question-group:hover {
    border-color: #cbd5e1;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.security-question-group:last-child {
    margin-bottom: 0;
}

.password-strength {
    height: 4px;
    border-radius: 2px;
    margin-top: 8px;
    transition: all 0.3s ease;
    background: #e5e7eb;
}

.password-strength.weak {
    background: linear-gradient(90deg, #ef4444 0%, #f87171 100%);
    width: 33%;
}

.password-strength.medium {
    background: linear-gradient(90deg, #f59e0b 0%, #fbbf24 100%);
    width: 66%;
}

.password-strength.strong {
    background: linear-gradient(90deg, #10b981 0%, #34d399 100%);
    width: 100%;
}

.btn-register {
    width: 100%;
    padding: 14px;
    font-size: 1rem;
    margin-top: 6px;
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn-register:hover {
    background: linear-gradient(135deg, #047857 0%, #059669 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.success-actions {
    margin-top: 20px;
    text-align: center;
}

.success-actions .btn {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 10px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.success-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

/* Form validation styles */
.form-group input.valid,
.form-group select.valid {
    border-color: #10b981;
    background-color: #f0fdf4;
}

.form-group input.invalid,
.form-group select.invalid {
    border-color: #ef4444;
    background-color: #fef2f2;
}

.validation-message {
    font-size: 0.8rem;
    margin-top: 4px;
    padding: 4px 8px;
    border-radius: 6px;
    display: none;
}

.validation-message.error {
    color: #dc2626;
    background: #fef2f2;
    border: 1px solid #fecaca;
    display: block;
}

.validation-message.success {
    color: #16a34a;
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    display: block;
}

/* Progress indicator */
.form-progress {
    background: #f1f5f9;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 24px;
    border: 1px solid #e2e8f0;
}

.progress-bar {
    background: #e2e8f0;
    height: 6px;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
    height: 100%;
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 3px;
}

.progress-text {
    font-size: 0.9rem;
    color: #64748b;
    text-align: center;
}

/* Responsive design */
@media (min-width: 900px) {
    .register-card {
        max-width: 1200px;
        width: 100%;
        margin: 2rem auto;
    }
    .register-form {
        padding: 2rem 2rem;
    }
    .form-row {
        gap: 4rem;
    }
}

@media (max-width: 899px) and (min-width: 769px) {
    .register-card {
        max-width: 100%;
        width: 94%;
        margin: 1rem auto;
    }

    .register-form {
        padding: 2rem 3rem;
    }

    .form-row {
        gap: 3rem;
    }
}

@media (max-width: 768px) {
    .register-card {
        margin: 1rem;
        max-width: calc(100% - 2rem);
        min-width: unset;
        width: calc(100% - 2rem);
    }

    .register-form {
        padding: 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-section .form-group:not(.form-row .form-group) {
        grid-template-columns: 1fr;
        gap: 0;
    }
    .security-question-group {
        padding: 16px;
    }
    #student-id-preview {
        font-size: 1rem;
        min-width: 150px;
    }
    .auth-links {
        flex-direction: column;
        gap: 12px;
    }
    .link-secondary {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .form-section h3 {
        font-size: 1rem;
    }
    
    .form-group input,
    .form-group select {
        padding: 10px 14px;
        font-size: 0.9rem;
    }
    
    .btn-register {
        padding: 16px;
        font-size: 1rem;
    }
}

/* Animation for form sections */
.form-section {
    animation: slideInUp 0.6s ease-out;
}

.form-section:nth-child(1) { animation-delay: 0.1s; }
.form-section:nth-child(2) { animation-delay: 0.2s; }
.form-section:nth-child(3) { animation-delay: 0.3s; }
.form-section:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
