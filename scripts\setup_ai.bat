@echo off
echo ========================================
echo AI Question Generator Setup
echo Ogbonnaya Onu Polytechnic, Aba
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo Python found! Installing required packages...
echo.

echo Installing OpenAI API client...
pip install openai==0.28.1

echo Installing MySQL connector...
pip install mysql-connector-python==8.1.0

echo Installing additional utilities...
pip install requests==2.31.0
pip install python-dotenv==1.0.0

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo The AI Question Generator is now ready to use.
echo.
echo Next steps:
echo 1. Configure your OpenAI API key in the LMS admin settings
echo 2. Use the "Generate Questions" feature in the admin panel
echo.
echo For help setting up your API key, visit the API Setup Guide
echo in the admin panel.
echo.
pause
