<?php
/**
 * API Test Script
 * Test OpenAI API connectivity
 */

require_once '../config/database.php';

// Require admin login
requireLogin('admin');

// Handle API test
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_api'])) {
    header('Content-Type: application/json');
    
    $apiKey = "********************************************************************************************************************************************************************";
    $model = 'gpt-3.5-turbo';
    
    // API key is hardcoded, so always available
    
    // Test API call
    $testPrompt = "Generate a simple test question about basic mathematics with 4 multiple choice options. Return only JSON format with question, options A-D, and correct_answer.";
    
    $data = [
        'model' => $model,
        'messages' => [
            [
                'role' => 'system',
                'content' => 'You are a helpful educational assistant.'
            ],
            [
                'role' => 'user',
                'content' => $testPrompt
            ]
        ],
        'max_tokens' => 500,
        'temperature' => 0.7
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/chat/completions');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $apiKey
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo json_encode([
            'success' => false,
            'message' => 'Connection error: ' . $error
        ]);
        exit;
    }
    
    $responseData = json_decode($response, true);
    
    if ($httpCode === 200 && isset($responseData['choices'][0]['message']['content'])) {
        echo json_encode([
            'success' => true,
            'message' => 'API connection successful!',
            'response' => $responseData['choices'][0]['message']['content'],
            'model' => $model,
            'usage' => $responseData['usage'] ?? null
        ]);
    } else {
        $errorMessage = 'API call failed';
        if (isset($responseData['error']['message'])) {
            $errorMessage = $responseData['error']['message'];
        }
        
        echo json_encode([
            'success' => false,
            'message' => $errorMessage,
            'http_code' => $httpCode,
            'response' => $responseData
        ]);
    }
    exit;
}

$pageTitle = 'API Test';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="../assets/images/logo.png" alt="Logo" onerror="this.style.display='none'">
                </div>
                <div class="admin-info">
                    <h3>Admin Panel</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>

            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="students.php" class="nav-item">
                    <i class="fas fa-users"></i>
                    <span>Students</span>
                </a>
                <a href="departments.php" class="nav-item">
                    <i class="fas fa-building"></i>
                    <span>Departments</span>
                </a>
                <a href="questions.php" class="nav-item">
                    <i class="fas fa-robot"></i>
                    <span>AI Questions</span>
                </a>
                <a href="generate-questions.php" class="nav-item">
                    <i class="fas fa-magic"></i>
                    <span>Generate Questions</span>
                </a>
                <a href="rewards.php" class="nav-item">
                    <i class="fas fa-trophy"></i>
                    <span>Rewards</span>
                </a>
                <a href="analytics.php" class="nav-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>Analytics</span>
                </a>
                <a href="settings.php" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
                
                <div class="nav-divider"></div>
                
                <a href="../index.php" class="nav-item nav-secondary">
                    <i class="fas fa-home"></i>
                    <span>Back to Site</span>
                </a>
                <a href="../logout.php" class="nav-item nav-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="content-header">
                <h1><i class="fas fa-flask"></i> API Connection Test</h1>
                <p>Test your OpenAI API configuration</p>
            </div>

            <div class="content-body">
                <div class="content-card">
                    <div class="card-header">
                        <h3><i class="fas fa-plug"></i> Test API Connection</h3>
                    </div>
                    <div class="card-content">
                        <div class="info-box">
                            <i class="fas fa-info-circle"></i>
                            <div class="info-content">
                                <h5>API Test</h5>
                                <p>This will send a simple test request to OpenAI to verify your API key and connection are working properly.</p>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn btn-primary" id="testApiBtn">
                                <i class="fas fa-flask"></i>
                                Test API Connection
                            </button>
                            <a href="settings.php#ai" class="btn btn-secondary">
                                <i class="fas fa-cog"></i>
                                API Settings
                            </a>
                        </div>

                        <div id="testResults" style="display: none; margin-top: 2rem;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('testApiBtn').addEventListener('click', function() {
            const btn = this;
            const resultsDiv = document.getElementById('testResults');
            
            // Show loading state
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML = `
                <div class="info-box">
                    <i class="fas fa-spinner fa-spin"></i>
                    <div class="info-content">
                        <h5>Testing API Connection...</h5>
                        <p>Please wait while we test your OpenAI API configuration.</p>
                    </div>
                </div>
            `;
            
            // Send test request
            fetch('test-api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'test_api=1'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <div class="info-box success">
                            <i class="fas fa-check-circle"></i>
                            <div class="info-content">
                                <h5>✅ API Test Successful!</h5>
                                <p>${data.message}</p>
                                <div style="margin-top: 1rem;">
                                    <strong>Model:</strong> ${data.model}<br>
                                    ${data.usage ? `<strong>Tokens Used:</strong> ${data.usage.total_tokens}` : ''}
                                </div>
                                ${data.response ? `
                                    <details style="margin-top: 1rem;">
                                        <summary style="cursor: pointer; font-weight: 600;">View Test Response</summary>
                                        <pre style="background: #f8f9fa; padding: 1rem; border-radius: 8px; margin-top: 0.5rem; white-space: pre-wrap; font-size: 0.9rem;">${data.response}</pre>
                                    </details>
                                ` : ''}
                            </div>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="info-box danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            <div class="info-content">
                                <h5>❌ API Test Failed</h5>
                                <p>${data.message}</p>
                                ${data.http_code ? `<p><strong>HTTP Code:</strong> ${data.http_code}</p>` : ''}
                                <div style="margin-top: 1rem;">
                                    <a href="api-setup.php" class="btn btn-secondary">
                                        <i class="fas fa-key"></i>
                                        Setup Guide
                                    </a>
                                    <a href="settings.php#ai" class="btn btn-primary">
                                        <i class="fas fa-cog"></i>
                                        Check Settings
                                    </a>
                                </div>
                            </div>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                resultsDiv.innerHTML = `
                    <div class="info-box danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <div class="info-content">
                            <h5>Connection Error</h5>
                            <p>Failed to test API connection. Please try again.</p>
                        </div>
                    </div>
                `;
            })
            .finally(() => {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-flask"></i> Test API Connection';
            });
        });
    </script>
</body>
</html>
