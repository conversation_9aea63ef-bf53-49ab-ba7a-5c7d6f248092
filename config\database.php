<?php
/**
 * Database Configuration for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'lms_ogbonnaya_onu';
    private $username = 'root';
    private $password = '';
    private $conn;
    
    public function getConnection() {
        $this->conn = null;

        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"
                )
            );
        } catch(PDOException $exception) {
            // Check if database doesn't exist
            if (strpos($exception->getMessage(), 'Unknown database') !== false) {
                $this->showDatabaseSetupMessage();
                exit();
            } else {
                echo "Connection error: " . $exception->getMessage();
            }
        }

        return $this->conn;
    }

    private function showDatabaseSetupMessage() {
        echo "<!DOCTYPE html>
        <html lang='en'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>Database Setup Required - AI-Powered LMS</title>
            <style>
                body { font-family: Arial, sans-serif; max-width: 600px; margin: 100px auto; padding: 20px; text-align: center; }
                .setup-card { background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
                .error { color: #dc2626; margin-bottom: 20px; }
                .btn { background: #6366f1; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; margin: 10px; }
                .btn:hover { background: #5856eb; }
                h1 { color: #1f2937; margin-bottom: 10px; }
                p { color: #6b7280; line-height: 1.6; }
            </style>
        </head>
        <body>
            <div class='setup-card'>
                <h1>🚀 Database Setup Required</h1>
                <div class='error'>
                    <strong>Database not found:</strong> The LMS database hasn't been created yet.
                </div>
                <p>Welcome to the AI-Powered LMS for Ogbonnaya Onu Polytechnic, Aba!</p>
                <p>To get started, you need to set up the database first.</p>
                <a href='setup-database.php' class='btn'>🔧 Setup Database</a>
                <p style='margin-top: 30px; font-size: 0.9em; color: #9ca3af;'>
                    This will create the database and all necessary tables for your LMS.
                </p>
            </div>
        </body>
        </html>";
    }
    
    public function closeConnection() {
        $this->conn = null;
    }
}

// Global function to get database connection
function getConnection() {
    $database = new Database();
    return $database->getConnection();
}

// Database utility functions
function executeQuery($sql, $params = []) {
    $database = new Database();
    $conn = $database->getConnection();
    
    try {
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch(PDOException $e) {
        error_log("Database query error: " . $e->getMessage());
        return false;
    }
}

function fetchOne($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetch() : false;
}

function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetchAll() : false;
}

function insertRecord($table, $data) {
    $columns = implode(',', array_keys($data));
    $placeholders = ':' . implode(', :', array_keys($data));
    
    $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
    $stmt = executeQuery($sql, $data);
    
    if ($stmt) {
        $database = new Database();
        $conn = $database->getConnection();
        return $conn->lastInsertId();
    }
    return false;
}

function updateRecord($table, $data, $where, $whereParams = []) {
    $setParts = [];
    foreach (array_keys($data) as $key) {
        $setParts[] = "{$key} = :{$key}";
    }
    $setClause = implode(', ', $setParts);
    
    $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
    $params = array_merge($data, $whereParams);
    
    return executeQuery($sql, $params) !== false;
}

function deleteRecord($table, $where, $params = []) {
    $sql = "DELETE FROM {$table} WHERE {$where}";
    return executeQuery($sql, $params) !== false;
}

// Security functions
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function generateSecureHash($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

function generateSessionId() {
    return bin2hex(random_bytes(32));
}

function generateStudentId($departmentCode) {
    $year = date('Y');
    $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    return $departmentCode . '/' . $year . '/' . $random;
}

// Session management
function startSecureSession() {
    if (session_status() == PHP_SESSION_NONE) {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', 1);
        ini_set('session.use_strict_mode', 1);
        session_start();
    }
}

function isLoggedIn() {
    startSecureSession();
    return isset($_SESSION['user_id']) && isset($_SESSION['user_type']);
}

function requireLogin($userType = null) {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit();
    }
    
    if ($userType && $_SESSION['user_type'] !== $userType) {
        header('Location: unauthorized.php');
        exit();
    }
}

function logout() {
    startSecureSession();
    
    // Update session in database
    if (isset($_SESSION['session_id'])) {
        updateRecord('user_sessions', 
            ['is_active' => 0], 
            'id = :session_id', 
            ['session_id' => $_SESSION['session_id']]
        );
    }
    
    session_destroy();
    header('Location: login.php');
    exit();
}

// Error handling
function logError($message, $file = '', $line = '') {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] Error: {$message}";
    if ($file) $logMessage .= " in {$file}";
    if ($line) $logMessage .= " on line {$line}";
    $logMessage .= "\n";
    
    error_log($logMessage, 3, 'logs/error.log');
}

// Response helpers
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit();
}

function redirectWithMessage($url, $message, $type = 'success') {
    startSecureSession();
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
    header("Location: {$url}");
    exit();
}

function getFlashMessage() {
    startSecureSession();
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}
?>
