<?php
/**
 * Student Dashboard for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Check if student is logged in
startSecureSession();
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'student') {
    header('Location: login.php');
    exit();
}

// Get student information
$student = fetchOne("
    SELECT s.*, d.name as department_name, al.level_name
    FROM students s
    JOIN departments d ON s.department_id = d.id
    JOIN academic_levels al ON s.academic_level_id = al.id
    WHERE s.id = :id
", ['id' => $_SESSION['user_id']]);

if (!$student) {
    session_destroy();
    header('Location: login.php');
    exit();
}

// Get student statistics
$stats = [
    'total_quizzes' => fetchOne("SELECT COUNT(*) as count FROM quiz_sessions WHERE student_id = :id", ['id' => $student['id']])['count'] ?? 0,
    'completed_quizzes' => fetchOne("SELECT COUNT(*) as count FROM quiz_sessions WHERE student_id = :id AND status = 'completed'", ['id' => $student['id']])['count'] ?? 0,
    'average_score' => fetchOne("SELECT AVG(score_percentage) as avg FROM quiz_sessions WHERE student_id = :id AND status = 'completed'", ['id' => $student['id']])['avg'] ?? 0,
    'total_questions_answered' => fetchOne("SELECT SUM(total_questions) as total FROM quiz_sessions WHERE student_id = :id AND status = 'completed'", ['id' => $student['id']])['total'] ?? 0
];

// Get recent quiz sessions
$recentQuizzes = fetchAll("
    SELECT qs.*, d.name as department_name, al.level_name
    FROM quiz_sessions qs
    JOIN departments d ON qs.department_id = d.id
    JOIN academic_levels al ON qs.academic_level_id = al.id
    WHERE qs.student_id = :id
    ORDER BY qs.created_at DESC
    LIMIT 5
", ['id' => $student['id']]);

// Get available question topics (department + level combinations)
$availableTopics = fetchAll("
    SELECT d.id as department_id, d.name as department_name, al.id as academic_level_id, al.level_name,
           COUNT(q.id) as question_count
    FROM departments d
    CROSS JOIN academic_levels al
    LEFT JOIN questions q ON d.id = q.department_id AND al.id = q.academic_level_id
    WHERE d.id = :dept_id AND al.id = :level_id
    GROUP BY d.id, al.id
    HAVING question_count > 0
", ['dept_id' => $student['department_id'], 'level_id' => $student['academic_level_id']]);

$pageTitle = 'Student Dashboard';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Student-specific styling */
        .sidebar-header h3 { color: #28a745; }
        .nav-item.active { background: linear-gradient(135deg, #28a745, #20c997); }
        .btn-primary { background: linear-gradient(135deg, #28a745, #20c997); }
        .stat-card.primary { background: linear-gradient(135deg, #28a745, #20c997); }
        .quiz-card { 
            background: white; 
            border-radius: 12px; 
            padding: 20px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
            margin-bottom: 20px;
            border-left: 4px solid #28a745;
        }
        .quiz-card h4 { color: #2c3e50; margin-bottom: 10px; }
        .quiz-meta { color: #6c757d; font-size: 14px; margin-bottom: 15px; }
        .quiz-actions { display: flex; gap: 10px; }
        .btn-start-quiz { 
            background: linear-gradient(135deg, #28a745, #20c997); 
            color: white; 
            padding: 8px 16px; 
            border-radius: 6px; 
            text-decoration: none; 
            font-size: 14px;
        }
        .btn-view-result { 
            background: #6c757d; 
            color: white; 
            padding: 8px 16px; 
            border-radius: 6px; 
            text-decoration: none; 
            font-size: 14px;
        }
        .performance-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .performance-excellent { background: #d4edda; color: #155724; }
        .performance-good { background: #d1ecf1; color: #0c5460; }
        .performance-average { background: #fff3cd; color: #856404; }
        .performance-poor { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Student Portal</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>

            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item active">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="quiz.php" class="nav-item">
                    <i class="fas fa-brain"></i>
                    <span>Take Quiz</span>
                </a>
                <a href="results.php" class="nav-item">
                    <i class="fas fa-chart-line"></i>
                    <span>My Results</span>
                </a>
                <a href="profile.php" class="nav-item">
                    <i class="fas fa-user"></i>
                    <span>Profile</span>
                </a>
                <a href="logout.php" class="nav-item">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <h4><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h4>
                        <p><?php echo htmlspecialchars($student['student_id']); ?></p>
                        <p><?php echo htmlspecialchars($student['department_name'] . ' - ' . $student['level_name']); ?></p>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="content-header">
                <div class="header-left">
                    <h1>Welcome Back, <?php echo htmlspecialchars($student['first_name']); ?>!</h1>
                    <p>Ready to continue your learning journey? Let's see what's new today.</p>
                </div>
                <div class="header-right">
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="location.href='quiz.php'">
                            <i class="fas fa-brain"></i>
                            Start New Quiz
                        </button>
                        <div class="header-stats">
                            <span class="stat-item">
                                <i class="fas fa-calendar"></i>
                                <?php echo date('M d, Y'); ?>
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-clock"></i>
                                <?php echo date('H:i'); ?>
                            </span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card primary">
                    <div class="stat-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['total_quizzes']); ?></h3>
                        <p>Total Quizzes</p>
                        <span class="stat-change positive">
                            <i class="fas fa-check"></i>
                            <?php echo number_format($stats['completed_quizzes']); ?> completed
                        </span>
                    </div>
                </div>

                <div class="stat-card success">
                    <div class="stat-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['average_score'], 1); ?>%</h3>
                        <p>Average Score</p>
                        <span class="stat-change <?php echo $stats['average_score'] >= 70 ? 'positive' : 'negative'; ?>">
                            <i class="fas fa-<?php echo $stats['average_score'] >= 70 ? 'arrow-up' : 'arrow-down'; ?>"></i>
                            <?php 
                            if ($stats['average_score'] >= 90) echo 'Excellent';
                            elseif ($stats['average_score'] >= 80) echo 'Very Good';
                            elseif ($stats['average_score'] >= 70) echo 'Good';
                            elseif ($stats['average_score'] >= 60) echo 'Average';
                            else echo 'Needs Improvement';
                            ?>
                        </span>
                    </div>
                </div>

                <div class="stat-card info">
                    <div class="stat-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['total_questions_answered']); ?></h3>
                        <p>Questions Answered</p>
                        <span class="stat-change positive">
                            <i class="fas fa-trophy"></i>
                            Keep learning!
                        </span>
                    </div>
                </div>

                <div class="stat-card warning">
                    <div class="stat-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo htmlspecialchars($student['department_name']); ?></h3>
                        <p>Your Department</p>
                        <span class="stat-change">
                            <i class="fas fa-level-up-alt"></i>
                            <?php echo htmlspecialchars($student['level_name']); ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="quiz.php" class="action-card students">
                    <div class="action-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="action-content">
                        <h3>Take New Quiz</h3>
                        <p>Start a new AI-generated quiz for your level</p>
                        <?php if (count($availableTopics) > 0): ?>
                            <span class="action-badge"><?php echo count($availableTopics); ?> topics available</span>
                        <?php endif; ?>
                    </div>
                    <div class="action-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </a>

                <a href="results.php" class="action-card departments">
                    <div class="action-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="action-content">
                        <h3>View Results</h3>
                        <p>Check your quiz performance and progress</p>
                        <?php if ($stats['completed_quizzes'] > 0): ?>
                            <span class="action-badge"><?php echo $stats['completed_quizzes']; ?> completed</span>
                        <?php endif; ?>
                    </div>
                    <div class="action-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </a>

                <a href="profile.php" class="action-card questions">
                    <div class="action-icon">
                        <i class="fas fa-user-cog"></i>
                    </div>
                    <div class="action-content">
                        <h3>Update Profile</h3>
                        <p>Manage your account settings and preferences</p>
                    </div>
                    <div class="action-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </a>
            </div>

            <!-- Content Grid -->
            <div class="content-grid">
                <!-- Recent Quizzes -->
                <div class="content-card">
                    <div class="card-header">
                        <h3><i class="fas fa-history"></i> Recent Quiz Sessions</h3>
                        <a href="results.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-eye"></i>
                            View All Results
                        </a>
                    </div>
                    <div class="card-content">
                        <?php if (empty($recentQuizzes)): ?>
                            <div class="empty-state">
                                <i class="fas fa-brain"></i>
                                <h4>No quizzes taken yet</h4>
                                <p>Start your first quiz to see your progress here</p>
                                <a href="quiz.php" class="btn btn-outline">Take Your First Quiz</a>
                            </div>
                        <?php else: ?>
                            <div class="quiz-list">
                                <?php foreach ($recentQuizzes as $quiz): ?>
                                    <div class="quiz-card">
                                        <h4><?php echo htmlspecialchars($quiz['department_name'] . ' - ' . $quiz['level_name']); ?></h4>
                                        <div class="quiz-meta">
                                            <span><i class="fas fa-calendar"></i> <?php echo date('M d, Y H:i', strtotime($quiz['created_at'])); ?></span>
                                            <span><i class="fas fa-question-circle"></i> <?php echo $quiz['total_questions']; ?> questions</span>
                                            <span><i class="fas fa-clock"></i> <?php echo ucfirst($quiz['status']); ?></span>
                                        </div>

                                        <?php if ($quiz['status'] === 'completed'): ?>
                                            <div class="quiz-score">
                                                <span class="performance-badge <?php
                                                    if ($quiz['score_percentage'] >= 90) echo 'performance-excellent';
                                                    elseif ($quiz['score_percentage'] >= 80) echo 'performance-good';
                                                    elseif ($quiz['score_percentage'] >= 70) echo 'performance-average';
                                                    else echo 'performance-poor';
                                                ?>">
                                                    <?php echo number_format($quiz['score_percentage'], 1); ?>%
                                                    (<?php echo $quiz['correct_answers']; ?>/<?php echo $quiz['total_questions']; ?>)
                                                </span>
                                            </div>
                                            <div class="quiz-actions">
                                                <a href="quiz-result.php?id=<?php echo $quiz['id']; ?>" class="btn-view-result">
                                                    <i class="fas fa-eye"></i> View Details
                                                </a>
                                            </div>
                                        <?php elseif ($quiz['status'] === 'in_progress'): ?>
                                            <div class="quiz-actions">
                                                <a href="quiz.php?resume=<?php echo $quiz['id']; ?>" class="btn-start-quiz">
                                                    <i class="fas fa-play"></i> Resume Quiz
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Available Topics -->
                <div class="content-card">
                    <div class="card-header">
                        <h3><i class="fas fa-book"></i> Available Study Topics</h3>
                        <a href="quiz.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i>
                            Start Quiz
                        </a>
                    </div>
                    <div class="card-content">
                        <?php if (empty($availableTopics)): ?>
                            <div class="empty-state">
                                <i class="fas fa-book-open"></i>
                                <h4>No questions available yet</h4>
                                <p>Questions for your department and level will appear here once generated by the admin</p>
                            </div>
                        <?php else: ?>
                            <div class="topics-list">
                                <?php foreach ($availableTopics as $topic): ?>
                                    <div class="topic-card">
                                        <div class="topic-info">
                                            <h4><?php echo htmlspecialchars($topic['department_name'] . ' - ' . $topic['level_name']); ?></h4>
                                            <p><?php echo number_format($topic['question_count']); ?> questions available</p>
                                        </div>
                                        <div class="topic-actions">
                                            <a href="quiz.php?dept=<?php echo $topic['department_id']; ?>&level=<?php echo $topic['academic_level_id']; ?>" class="btn-start-quiz">
                                                <i class="fas fa-play"></i> Start Quiz
                                            </a>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="../assets/js/admin-dashboard.js"></script>
    <script>
        // Student dashboard specific functions
        function startQuiz(departmentId, levelId) {
            window.location.href = `quiz.php?dept=${departmentId}&level=${levelId}`;
        }

        function viewQuizResult(quizId) {
            window.location.href = `quiz-result.php?id=${quizId}`;
        }

        // Auto-refresh stats every 30 seconds
        setInterval(function() {
            // You can add AJAX calls here to refresh stats if needed
        }, 30000);
    </script>
</body>
</html>
