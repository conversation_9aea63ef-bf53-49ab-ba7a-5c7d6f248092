/**
 * Authentication JavaScript for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeAuth();
});

function initializeAuth() {
    const form = document.querySelector('.auth-form');
    const passwordInput = document.getElementById('password');
    const usernameInput = document.getElementById('username');
    
    // Add form validation
    if (form) {
        form.addEventListener('submit', validateAuthForm);
    }
    
    // Add input enhancements
    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            clearFieldError(this);
        });
    }
    
    if (usernameInput) {
        usernameInput.addEventListener('input', function() {
            clearFieldError(this);
        });
    }
    
    // Add enter key support
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && e.target.tagName !== 'BUTTON') {
            const submitBtn = document.querySelector('.btn-login');
            if (submitBtn && !submitBtn.disabled) {
                submitBtn.click();
            }
        }
    });
    
    // Auto-focus first input
    const firstInput = document.querySelector('input[type="text"], input[type="email"]');
    if (firstInput) {
        firstInput.focus();
    }
    
    // Add loading animation to buttons
    addButtonLoadingAnimation();
}

function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
        button.setAttribute('aria-label', 'Hide password');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
        button.setAttribute('aria-label', 'Show password');
    }
}

function validateAuthForm(e) {
    const username = document.getElementById('username')?.value.trim();
    const email = document.getElementById('email')?.value.trim();
    const password = document.getElementById('password')?.value;
    const confirmPassword = document.getElementById('confirm_password')?.value;
    
    let isValid = true;
    let errorMessage = '';
    
    // Clear previous errors
    clearAllFieldErrors();
    
    // Username/Email validation
    if (username !== undefined) {
        if (!username) {
            errorMessage = 'Username is required.';
            isValid = false;
            addFieldError('username');
        }
    }
    
    if (email !== undefined) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!email) {
            errorMessage = 'Email is required.';
            isValid = false;
            addFieldError('email');
        } else if (!emailRegex.test(email)) {
            errorMessage = 'Please enter a valid email address.';
            isValid = false;
            addFieldError('email');
        }
    }
    
    // Password validation
    if (!password) {
        errorMessage = 'Password is required.';
        isValid = false;
        addFieldError('password');
    } else if (password.length < 6) {
        errorMessage = 'Password must be at least 6 characters long.';
        isValid = false;
        addFieldError('password');
    }
    
    // Confirm password validation (for registration)
    if (confirmPassword !== undefined) {
        if (!confirmPassword) {
            errorMessage = 'Please confirm your password.';
            isValid = false;
            addFieldError('confirm_password');
        } else if (password !== confirmPassword) {
            errorMessage = 'Passwords do not match.';
            isValid = false;
            addFieldError('confirm_password');
        }
    }
    
    if (!isValid) {
        e.preventDefault();
        showError(errorMessage);
        return false;
    }
    
    // Show loading state
    const submitBtn = document.querySelector('.btn-login, .btn-register');
    if (submitBtn) {
        setButtonLoading(submitBtn, true);
    }
    
    return true;
}

function addFieldError(fieldId) {
    const field = document.getElementById(fieldId);
    if (field) {
        field.classList.add('error');
        field.style.borderColor = '#ef4444';
        field.style.backgroundColor = '#fef2f2';
    }
}

function clearFieldError(field) {
    field.classList.remove('error');
    field.style.borderColor = '';
    field.style.backgroundColor = '';
}

function clearAllFieldErrors() {
    const fields = document.querySelectorAll('input');
    fields.forEach(field => clearFieldError(field));
}

function showError(message) {
    // Remove existing error alerts
    const existingAlert = document.querySelector('.alert-error');
    if (existingAlert) {
        existingAlert.remove();
    }
    
    // Create new error alert
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-error';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-circle"></i>
        ${message}
    `;
    
    // Insert at the beginning of auth-body
    const authBody = document.querySelector('.auth-body');
    authBody.insertBefore(alertDiv, authBody.firstChild);
    
    // Scroll to error
    alertDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.style.opacity = '0';
            alertDiv.style.transform = 'translateY(-10px)';
            setTimeout(() => alertDiv.remove(), 300);
        }
    }, 5000);
}

function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle"></i>
        ${message}
    `;
    
    const authBody = document.querySelector('.auth-body');
    authBody.insertBefore(alertDiv, authBody.firstChild);
    
    alertDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

function setButtonLoading(button, isLoading) {
    if (isLoading) {
        button.dataset.originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Please wait...';
        button.disabled = true;
        button.style.opacity = '0.8';
    } else {
        button.innerHTML = button.dataset.originalText || button.innerHTML;
        button.disabled = false;
        button.style.opacity = '1';
    }
}

function addButtonLoadingAnimation() {
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            if (this.type === 'submit') {
                // Add ripple effect
                const ripple = document.createElement('span');
                ripple.className = 'ripple';
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            }
        });
    });
}

// Add department selection functionality for student registration
function initializeDepartmentSelection() {
    const departmentSelect = document.getElementById('department');
    const levelSelect = document.getElementById('academic_level');
    
    if (departmentSelect && levelSelect) {
        departmentSelect.addEventListener('change', function() {
            // You can add department-specific logic here if needed
            updateStudentIdPreview();
        });
        
        levelSelect.addEventListener('change', function() {
            updateStudentIdPreview();
        });
    }
}

function updateStudentIdPreview() {
    const departmentSelect = document.getElementById('department');
    const preview = document.getElementById('student-id-preview');
    
    if (departmentSelect && preview) {
        const selectedOption = departmentSelect.options[departmentSelect.selectedIndex];
        const departmentCode = selectedOption.dataset.code || 'XXX';
        const year = new Date().getFullYear();
        const randomNum = Math.floor(Math.random() * 9000) + 1000;
        
        preview.textContent = `${departmentCode}/${year}/${randomNum}`;
    }
}

// Initialize department selection if on registration page
if (document.getElementById('department')) {
    initializeDepartmentSelection();
}

// Add CSS for enhanced interactions
const style = document.createElement('style');
style.textContent = `
    .form-group input.error {
        animation: shake 0.5s ease-in-out;
    }
    
    .btn {
        position: relative;
        overflow: hidden;
    }
    
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .alert {
        transition: all 0.3s ease;
    }
    
    .form-group input:focus {
        animation: focusPulse 0.3s ease;
    }
    
    @keyframes focusPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.02); }
        100% { transform: scale(1); }
    }
`;
document.head.appendChild(style);
