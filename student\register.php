<?php
/**
 * Student Registration Page for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Check if registration is open
$registrationOpen = fetchOne("SELECT setting_value FROM system_settings WHERE setting_key = 'registration_open'");
if (!$registrationOpen || $registrationOpen['setting_value'] != '1') {
    $error = 'Student registration is currently closed. Please contact the administration.';
}

// Get departments and academic levels
$departments = fetchAll("SELECT id, name, code FROM departments ORDER BY name");
$academicLevels = fetchAll("SELECT id, level_name, level_code FROM academic_levels ORDER BY id");
$securityQuestions = fetchAll("SELECT id, question FROM security_questions ORDER BY id");

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$error) {
    $firstName = sanitizeInput($_POST['first_name']);
    $lastName = sanitizeInput($_POST['last_name']);
    $email = sanitizeInput($_POST['email']);
    $phone = sanitizeInput($_POST['phone']);
    $matriculationNo = strtoupper(sanitizeInput($_POST['matriculation_no']));
    $password = $_POST['password'];
    $confirmPassword = $_POST['confirm_password'];
    $departmentId = (int)$_POST['department_id'];
    $academicLevelId = (int)$_POST['academic_level_id'];
    
    // Security questions
    $securityAnswers = [
        ['question_id' => (int)$_POST['security_question_1'], 'answer' => sanitizeInput($_POST['security_answer_1'])],
        ['question_id' => (int)$_POST['security_question_2'], 'answer' => sanitizeInput($_POST['security_answer_2'])],
        ['question_id' => (int)$_POST['security_question_3'], 'answer' => sanitizeInput($_POST['security_answer_3'])]
    ];
    
    // Validation
    if (empty($firstName) || empty($lastName) || empty($email) || empty($matriculationNo) || empty($password)) {
        $error = 'Please fill in all required fields.';
    } elseif ($password !== $confirmPassword) {
        $error = 'Passwords do not match.';
    } elseif (strlen($password) < 8) {
        $error = 'Password must be at least 8 characters long.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } elseif ($departmentId <= 0 || $academicLevelId <= 0) {
        $error = 'Please select your department and academic level.';
    } else {
        // Check for duplicate email
        $existingStudent = fetchOne("SELECT id FROM students WHERE email = :email", ['email' => $email]);
        if ($existingStudent) {
            $error = 'An account with this email already exists.';
        } else {
            // Check for duplicate matriculation number
            $existingMatNo = fetchOne("SELECT id FROM students WHERE student_id = :mat_no", ['mat_no' => $matriculationNo]);
            if ($existingMatNo) {
                $error = 'Matriculation number already exists in the database. Please verify your matriculation number.';
            } else {
            // Validate security questions
            $questionIds = array_column($securityAnswers, 'question_id');
            if (count(array_unique($questionIds)) !== 3) {
                $error = 'Please select three different security questions.';
            } else {
                foreach ($securityAnswers as $answer) {
                    if (empty($answer['answer'])) {
                        $error = 'Please answer all security questions.';
                        break;
                    }
                }
            }
        }
    }

    if (!$error) {
        try {
            // Hash password
            $passwordHash = generateSecureHash($password);

            // Insert student
            $newStudentId = insertRecord('students', [
                'student_id' => $matriculationNo,
                'first_name' => $firstName,
                'last_name' => $lastName,
                'email' => $email,
                'phone' => $phone,
                'password_hash' => $passwordHash,
                'department_id' => $departmentId,
                'academic_level_id' => $academicLevelId,
                'is_approved' => 0
            ]);
            
            if ($newStudentId) {
                // Insert security answers
                foreach ($securityAnswers as $answer) {
                    insertRecord('student_security_answers', [
                        'student_id' => $newStudentId,
                        'question_id' => $answer['question_id'],
                        'answer_hash' => generateSecureHash(strtolower($answer['answer']))
                    ]);
                }
                
                $success = "Registration successful! Your Matriculation Number is: <strong>{$matriculationNo}</strong><br>
                           Your account is pending approval. You will be notified once approved.";
            } else {
                $error = 'Registration failed. Please try again.';
            }
        } catch (Exception $e) {
            $error = 'Registration failed: ' . $e->getMessage();
        }
    }
}
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Registration - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/auth.css">
    <link rel="stylesheet" href="../assets/css/register.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="auth-container">
        <div class="auth-card register-card">
            <div class="auth-header">
                <div class="logo-container">
                    <img src="../images/logo.jpg" alt="Ogbonnaya Onu Polytechnic Logo" class="logo">
                </div>
                <h1>Student Registration</h1>
                <p>AI-Powered Learning Management System</p>
                <p class="school-name">Ogbonnaya Onu Polytechnic, Aba</p>
            </div>
            
            <div class="auth-body">
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo $success; ?>
                        <div class="success-actions">
                            <a href="login.php" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt"></i>
                                Login
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <form method="POST" class="auth-form register-form">
                        <!-- Personal Information -->
                        <div class="form-section">
                            <h3><i class="fas fa-user"></i> Personal Information</h3>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="first_name">First Name *</label>
                                    <input type="text" id="first_name" name="first_name" required 
                                           value="<?php echo isset($_POST['first_name']) ? htmlspecialchars($_POST['first_name']) : ''; ?>">
                                </div>
                                <div class="form-group">
                                    <label for="last_name">Last Name *</label>
                                    <input type="text" id="last_name" name="last_name" required 
                                           value="<?php echo isset($_POST['last_name']) ? htmlspecialchars($_POST['last_name']) : ''; ?>">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="email">Email Address *</label>
                                <input type="email" id="email" name="email" required 
                                       value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="phone">Phone Number</label>
                                <input type="tel" id="phone" name="phone" 
                                       value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                            </div>
                        </div>
                        
                        <!-- Academic Information -->
                        <div class="form-section">
                            <h3><i class="fas fa-graduation-cap"></i> Academic Information</h3>
                            
                            <div class="form-group">
                                <label for="department_id">Department *</label>
                                <select id="department_id" name="department_id" required>
                                    <option value="">Select your department</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?php echo $dept['id']; ?>" 
                                                data-code="<?php echo $dept['code']; ?>"
                                                <?php echo (isset($_POST['department_id']) && $_POST['department_id'] == $dept['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($dept['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="academic_level_id">Academic Level *</label>
                                <select id="academic_level_id" name="academic_level_id" required>
                                    <option value="">Select your level</option>
                                    <?php foreach ($academicLevels as $level): ?>
                                        <option value="<?php echo $level['id']; ?>"
                                                <?php echo (isset($_POST['academic_level_id']) && $_POST['academic_level_id'] == $level['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($level['level_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="matriculation_no">Matriculation Number *</label>
                                <input type="text" id="matriculation_no" name="matriculation_no" required
                                       placeholder="Enter your matriculation number"
                                       value="<?php echo isset($_POST['matriculation_no']) ? htmlspecialchars($_POST['matriculation_no']) : ''; ?>">
                                <small class="form-help">Enter your unique matriculation number as provided by the institution</small>
                            </div>
                        </div>
                        
                        <!-- Password Section -->
                        <div class="form-section">
                            <h3><i class="fas fa-lock"></i> Password</h3>
                            
                            <div class="form-group">
                                <label for="password">Password *</label>
                                <div class="password-input">
                                    <input type="password" id="password" name="password" required minlength="8">
                                    <button type="button" class="toggle-password" onclick="togglePassword('password')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="password-strength" id="password-strength"></div>
                            </div>
                            
                            <div class="form-group">
                                <label for="confirm_password">Confirm Password *</label>
                                <div class="password-input">
                                    <input type="password" id="confirm_password" name="confirm_password" required minlength="8">
                                    <button type="button" class="toggle-password" onclick="togglePassword('confirm_password')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Security Questions -->
                        <div class="form-section">
                            <h3><i class="fas fa-shield-alt"></i> Security Questions</h3>
                            <p class="section-description">Choose 3 different security questions for password recovery</p>
                            
                            <?php for ($i = 1; $i <= 3; $i++): ?>
                                <div class="security-question-group">
                                    <div class="form-group">
                                        <label for="security_question_<?php echo $i; ?>">Security Question <?php echo $i; ?> *</label>
                                        <select id="security_question_<?php echo $i; ?>" name="security_question_<?php echo $i; ?>" required>
                                            <option value="">Select a question</option>
                                            <?php foreach ($securityQuestions as $question): ?>
                                                <option value="<?php echo $question['id']; ?>"
                                                        <?php echo (isset($_POST["security_question_{$i}"]) && $_POST["security_question_{$i}"] == $question['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($question['question']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="security_answer_<?php echo $i; ?>">Answer *</label>
                                        <input type="text" id="security_answer_<?php echo $i; ?>" name="security_answer_<?php echo $i; ?>" required
                                               value="<?php echo isset($_POST["security_answer_{$i}"]) ? htmlspecialchars($_POST["security_answer_{$i}"]) : ''; ?>">
                                    </div>
                                </div>
                            <?php endfor; ?>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-register">
                            <i class="fas fa-user-plus"></i>
                            Register Account
                        </button>
                    </form>
                <?php endif; ?>
                
                <div class="auth-links">
                    <a href="login.php" class="link-secondary">
                        <i class="fas fa-sign-in-alt"></i>
                        Already have an account? Login
                    </a>
                    <a href="../index.php" class="link-secondary">
                        <i class="fas fa-home"></i>
                        Back to Home
                    </a>
                </div>
            </div>
            
            <div class="auth-footer">
                <div class="security-info">
                    <i class="fas fa-shield-alt"></i>
                    <span>Your information is secure and encrypted</span>
                </div>
                <p>&copy; 2025 Ogbonnaya Onu Polytechnic, Aba. All rights reserved.</p>
            </div>
        </div>
    </div>
    
    <script src="../assets/js/auth.js"></script>
    <script src="../assets/js/register.js"></script>
</body>
</html>
