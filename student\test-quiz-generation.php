<?php
/**
 * Direct Test of Quiz Generation System
 */

require_once '../config/database.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Test Quiz Generation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; padding: 10px; background: #d4edda; border-radius: 5px; margin: 10px 0; }
        .error { color: red; padding: 10px; background: #f8d7da; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; padding: 10px; background: #d1ecf1; border-radius: 5px; margin: 10px 0; }
        .question { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>";

echo "<h1>🧪 Direct Test of Quiz Generation System</h1>";

/**
 * Call Gemini API to generate questions (copied from quiz.php)
 */
function callGeminiForQuestions($department, $academicLevel, $numQuestions, $apiKey) {
    $prompt = "Generate $numQuestions multiple-choice questions for {$department['name']} students at {$academicLevel['level_name']} level. 

Focus on practical and theoretical knowledge relevant to {$department['name']} curriculum at polytechnic level.

Return ONLY a valid JSON array with this exact structure:
[
  {
    \"question_text\": \"Question here?\",
    \"option_a\": \"First option\",
    \"option_b\": \"Second option\", 
    \"option_c\": \"Third option\",
    \"option_d\": \"Fourth option\",
    \"correct_answer\": \"A\",
    \"explanation\": \"Why this answer is correct\",
    \"difficulty\": \"medium\"
  }
]

Make questions diverse, practical, and appropriate for polytechnic {$academicLevel['level_name']} students studying {$department['name']}.
Return only the JSON array, no additional text or formatting.";

    $data = [
        'contents' => [
            [
                'parts' => [
                    [
                        'text' => $prompt
                    ]
                ]
            ]
        ],
        'generationConfig' => [
            'temperature' => 0.7,
            'topK' => 40,
            'topP' => 0.95,
            'maxOutputTokens' => 3000
        ]
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=' . $apiKey);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    echo "<div class='info'>📡 Gemini API Response Code: $httpCode</div>";
    if ($curlError) {
        echo "<div class='error'>❌ cURL Error: $curlError</div>";
    }

    if ($httpCode === 200 && $response) {
        $result = json_decode($response, true);
        if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
            $content = trim($result['candidates'][0]['content']['parts'][0]['text']);
            
            // Clean up the response - remove markdown formatting if present
            $content = preg_replace('/```json\s*/', '', $content);
            $content = preg_replace('/```\s*$/', '', $content);
            $content = trim($content);
            
            $questions = json_decode($content, true);
            if (is_array($questions) && !empty($questions)) {
                return $questions;
            } else {
                echo "<div class='error'>❌ Failed to parse Gemini response as JSON</div>";
                echo "<pre>Content: " . htmlspecialchars($content) . "</pre>";
            }
        } else {
            echo "<div class='error'>❌ Unexpected Gemini API response structure</div>";
            echo "<pre>Response: " . htmlspecialchars($response) . "</pre>";
        }
    } else {
        echo "<div class='error'>❌ Gemini API request failed with HTTP code: $httpCode</div>";
        if ($response) {
            echo "<pre>Response: " . htmlspecialchars($response) . "</pre>";
        }
    }
    
    return [];
}

/**
 * Generate quiz questions (copied from quiz.php)
 */
function generateQuizQuestions($student) {
    try {
        // Check if questions already exist for this department and level
        $existingQuestions = fetchAll("
            SELECT * FROM questions 
            WHERE department_id = :dept_id AND academic_level_id = :level_id
            ORDER BY RAND() LIMIT 10
        ", [
            'dept_id' => $student['department_id'],
            'level_id' => $student['academic_level_id']
        ]);
        
        if (count($existingQuestions) >= 5) {
            // Use existing questions
            echo "<div class='info'>📚 Using existing questions from database</div>";
            return [
                'success' => true,
                'questions' => array_slice($existingQuestions, 0, 10),
                'count' => min(10, count($existingQuestions)),
                'source' => 'existing'
            ];
        }
        
        // Generate new questions using AI
        $department = fetchOne("SELECT * FROM departments WHERE id = :id", ['id' => $student['department_id']]);
        $academicLevel = fetchOne("SELECT * FROM academic_levels WHERE id = :id", ['id' => $student['academic_level_id']]);
        
        if (!$department || !$academicLevel) {
            return ['success' => false, 'error' => 'Invalid department or academic level'];
        }
        
        echo "<div class='info'>🎯 Generating questions for: {$department['name']} - {$academicLevel['level_name']}</div>";
        
        // Call AI generation function with Gemini API
        $apiKey = "AIzaSyBx_2EL2AiM9UvEEusJPlAXsXN4HNgTkiw";
        $generatedQuestions = callGeminiForQuestions($department, $academicLevel, 3, $apiKey);
        
        if (!empty($generatedQuestions)) {
            echo "<div class='success'>✅ Generated " . count($generatedQuestions) . " questions successfully!</div>";
            
            // Save generated questions to database
            $savedQuestions = [];
            foreach ($generatedQuestions as $q) {
                $questionId = insertRecord('questions', [
                    'department_id' => $student['department_id'],
                    'academic_level_id' => $student['academic_level_id'],
                    'question_text' => $q['question_text'],
                    'option_a' => $q['option_a'],
                    'option_b' => $q['option_b'],
                    'option_c' => $q['option_c'],
                    'option_d' => $q['option_d'],
                    'correct_answer' => $q['correct_answer'],
                    'explanation' => $q['explanation'] ?? '',
                    'difficulty' => $q['difficulty'] ?? 'medium'
                ]);
                
                if ($questionId) {
                    $savedQuestions[] = array_merge($q, ['id' => $questionId]);
                    echo "<div class='success'>💾 Saved question to database with ID: $questionId</div>";
                }
            }
            
            return [
                'success' => true,
                'questions' => $savedQuestions,
                'count' => count($savedQuestions),
                'source' => 'generated'
            ];
        }
        
        return ['success' => false, 'error' => 'Failed to generate questions with AI'];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

try {
    // Get a test student
    $student = fetchOne("
        SELECT s.*, d.name as department_name, al.level_name
        FROM students s
        JOIN departments d ON s.department_id = d.id
        JOIN academic_levels al ON s.academic_level_id = al.id
        WHERE s.student_id = 'TEST001'
    ");

    if (!$student) {
        echo "<div class='error'>❌ Test student not found. Please create a test student first.</div>";
        echo "<p><a href='create-test-student.php'>👨‍🎓 Create Test Student</a></p>";
        echo "</body></html>";
        exit;
    }

    echo "<div class='info'>👨‍🎓 Testing with student: {$student['first_name']} {$student['last_name']} ({$student['department_name']} - {$student['level_name']})</div>";

    // Test question generation
    $result = generateQuizQuestions($student);

    if ($result['success']) {
        echo "<div class='success'>🎉 Quiz generation successful!</div>";
        echo "<div class='info'>📊 Generated {$result['count']} questions from {$result['source']} source</div>";
        
        echo "<div class='info'>📝 Generated Questions:</div>";
        foreach ($result['questions'] as $index => $question) {
            echo "<div class='question'>";
            echo "<h4>Question " . ($index + 1) . ":</h4>";
            echo "<p><strong>Q:</strong> " . htmlspecialchars($question['question_text']) . "</p>";
            echo "<p><strong>A:</strong> " . htmlspecialchars($question['option_a']) . "</p>";
            echo "<p><strong>B:</strong> " . htmlspecialchars($question['option_b']) . "</p>";
            echo "<p><strong>C:</strong> " . htmlspecialchars($question['option_c']) . "</p>";
            echo "<p><strong>D:</strong> " . htmlspecialchars($question['option_d']) . "</p>";
            echo "<p><strong>Correct:</strong> " . htmlspecialchars($question['correct_answer']) . "</p>";
            echo "<p><strong>Explanation:</strong> " . htmlspecialchars($question['explanation'] ?? 'N/A') . "</p>";
            echo "<p><strong>Difficulty:</strong> " . htmlspecialchars($question['difficulty'] ?? 'N/A') . "</p>";
            echo "</div>";
        }
        
        echo "<div class='success'>✅ <strong>SUCCESS!</strong> The quiz generation system is working correctly!</div>";
        echo "<p><a href='quiz.php'>🧠 Test the full quiz system</a></p>";
        echo "<p><a href='../login.php'>🔐 Login to test as student</a></p>";
        
    } else {
        echo "<div class='error'>❌ Quiz generation failed: " . htmlspecialchars($result['error']) . "</div>";
    }

} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}

echo "</body></html>";
?>
