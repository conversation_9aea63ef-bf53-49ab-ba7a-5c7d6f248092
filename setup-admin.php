<?php
/**
 * Admin Setup Script for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 * 
 * This script allows the initial setup of the admin account
 * Run this only once during system installation
 */

require_once 'config/database.php';

// Check if admin is already setup
$existingAdmin = fetchOne("SELECT id, is_setup FROM admins WHERE username = 'admin'");

if ($existingAdmin && $existingAdmin['is_setup']) {
    header('Location: admin/login.php');
    exit();
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $password = sanitizeInput($_POST['password']);
    $confirmPassword = sanitizeInput($_POST['confirm_password']);
    $email = sanitizeInput($_POST['email']);
    
    // Validation
    if (empty($password) || empty($confirmPassword) || empty($email)) {
        $error = 'All fields are required.';
    } elseif ($password !== $confirmPassword) {
        $error = 'Passwords do not match.';
    } elseif (strlen($password) < 8) {
        $error = 'Password must be at least 8 characters long.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } else {
        // Hash password
        $passwordHash = generateSecureHash($password);
        
        try {
            if ($existingAdmin) {
                // Update existing admin
                $updated = updateRecord('admins', [
                    'password_hash' => $passwordHash,
                    'email' => $email,
                    'is_setup' => 1
                ], 'username = :username', ['username' => 'admin']);
            } else {
                // Create new admin
                $adminId = insertRecord('admins', [
                    'username' => 'admin',
                    'password_hash' => $passwordHash,
                    'email' => $email,
                    'is_setup' => 1
                ]);
                $updated = $adminId !== false;
            }
            
            if ($updated) {
                $success = 'Admin account setup successfully! You can now login.';
            } else {
                $error = 'Failed to setup admin account. Please try again.';
            }
        } catch (Exception $e) {
            $error = 'Database error: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Setup - AI-Powered LMS</title>
    <link rel="stylesheet" href="assets/css/setup.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="setup-container">
        <div class="setup-card">
            <div class="setup-header">
                <div class="logo-container">
                    <img src="images/logo.jpg" alt="Ogbonnaya Onu Polytechnic Logo" class="logo">
                </div>
                <h1>Admin Setup</h1>
                <p>AI-Powered Learning Management System</p>
                <p class="school-name">Ogbonnaya Onu Polytechnic, Aba</p>
            </div>
            
            <div class="setup-body">
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo htmlspecialchars($success); ?>
                        <div class="success-actions">
                            <a href="admin/login.php" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt"></i>
                                Login to Admin Panel
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <form method="POST" class="setup-form">
                        <div class="form-group">
                            <label for="email">
                                <i class="fas fa-envelope"></i>
                                Admin Email
                            </label>
                            <input 
                                type="email" 
                                id="email" 
                                name="email" 
                                required 
                                placeholder="Enter admin email address"
                                value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                            >
                        </div>
                        
                        <div class="form-group">
                            <label for="password">
                                <i class="fas fa-lock"></i>
                                Admin Password
                            </label>
                            <div class="password-input">
                                <input 
                                    type="password" 
                                    id="password" 
                                    name="password" 
                                    required 
                                    placeholder="Enter secure password (min 8 characters)"
                                    minlength="8"
                                >
                                <button type="button" class="toggle-password" onclick="togglePassword('password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-strength" id="password-strength"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm_password">
                                <i class="fas fa-lock"></i>
                                Confirm Password
                            </label>
                            <div class="password-input">
                                <input 
                                    type="password" 
                                    id="confirm_password" 
                                    name="confirm_password" 
                                    required 
                                    placeholder="Confirm your password"
                                    minlength="8"
                                >
                                <button type="button" class="toggle-password" onclick="togglePassword('confirm_password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="security-notice">
                            <i class="fas fa-shield-alt"></i>
                            <div>
                                <strong>Security Notice:</strong>
                                <p>This admin account will have full access to the system. Please choose a strong password and keep it secure.</p>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-setup">
                            <i class="fas fa-cog"></i>
                            Setup Admin Account
                        </button>
                    </form>
                <?php endif; ?>
            </div>
            
            <div class="setup-footer">
                <p>&copy; 2025 Ogbonnaya Onu Polytechnic, Aba. All rights reserved.</p>
                <p class="version">LMS Version 1.0</p>
            </div>
        </div>
    </div>
    
    <script src="assets/js/setup.js"></script>
</body>
</html>
