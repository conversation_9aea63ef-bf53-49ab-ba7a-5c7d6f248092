<?php
/**
 * Database Migration: Remove Subjects System
 * Transform LMS to use Department + Academic Level directly
 * 
 * This migration:
 * 1. Backs up existing data
 * 2. Updates questions table structure
 * 3. Updates quiz_sessions table structure  
 * 4. Updates student_progress table structure
 * 5. Removes subjects table
 */

require_once '../config/database.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>🔄 Database Migration - Remove Subjects System</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .step { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 10px 0; }
        h1, h2 { color: #333; }
        .backup-info { background: #e2e3e5; padding: 10px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>";

echo "<h1>🔄 Database Migration: Remove Subjects System</h1>";
echo "<p><strong>Transforming LMS to use Department + Academic Level directly</strong></p>";

try {
    $pdo = getConnection();
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='step'>";
    echo "<h2>📋 Step 1: Pre-Migration Analysis</h2>";
    
    // Check current data
    $subjectsCount = fetchOne("SELECT COUNT(*) as count FROM subjects")['count'];
    $questionsCount = fetchOne("SELECT COUNT(*) as count FROM questions")['count'];
    $quizSessionsCount = fetchOne("SELECT COUNT(*) as count FROM quiz_sessions")['count'];
    $progressCount = fetchOne("SELECT COUNT(*) as count FROM student_progress")['count'];
    
    echo "<div class='info'>";
    echo "<h3>📊 Current Data Summary:</h3>";
    echo "<ul>";
    echo "<li><strong>Subjects:</strong> {$subjectsCount} records</li>";
    echo "<li><strong>Questions:</strong> {$questionsCount} records</li>";
    echo "<li><strong>Quiz Sessions:</strong> {$quizSessionsCount} records</li>";
    echo "<li><strong>Student Progress:</strong> {$progressCount} records</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>💾 Step 2: Create Backup Tables</h2>";
    
    // Create backup tables
    $backupTables = [
        'subjects_backup' => 'subjects',
        'questions_backup' => 'questions', 
        'quiz_sessions_backup' => 'quiz_sessions',
        'student_progress_backup' => 'student_progress'
    ];
    
    foreach ($backupTables as $backupTable => $originalTable) {
        try {
            $pdo->exec("DROP TABLE IF EXISTS {$backupTable}");
            $pdo->exec("CREATE TABLE {$backupTable} AS SELECT * FROM {$originalTable}");
            $count = fetchOne("SELECT COUNT(*) as count FROM {$backupTable}")['count'];
            echo "<div class='success'>✅ Backup created: {$backupTable} ({$count} records)</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Failed to backup {$originalTable}: " . $e->getMessage() . "</div>";
        }
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>🔧 Step 3: Update Questions Table Structure</h2>";
    
    // Add new columns to questions table
    try {
        $pdo->exec("ALTER TABLE questions ADD COLUMN department_id INT NULL AFTER id");
        $pdo->exec("ALTER TABLE questions ADD COLUMN academic_level_id INT NULL AFTER department_id");
        echo "<div class='success'>✅ Added department_id and academic_level_id columns to questions table</div>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "<div class='info'>ℹ️ Columns already exist in questions table</div>";
        } else {
            echo "<div class='error'>❌ Failed to add columns: " . $e->getMessage() . "</div>";
        }
    }
    
    // Migrate existing questions data
    try {
        $pdo->exec("
            UPDATE questions q 
            JOIN subjects s ON q.subject_id = s.id 
            SET q.department_id = s.department_id, 
                q.academic_level_id = s.academic_level_id
            WHERE q.department_id IS NULL
        ");
        
        $updatedQuestions = $pdo->exec("SELECT COUNT(*) FROM questions WHERE department_id IS NOT NULL");
        echo "<div class='success'>✅ Migrated existing questions to use department + academic level</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Failed to migrate questions: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>🎯 Step 4: Update Quiz Sessions Table Structure</h2>";
    
    // Add new columns to quiz_sessions table
    try {
        $pdo->exec("ALTER TABLE quiz_sessions ADD COLUMN department_id INT NULL AFTER student_id");
        $pdo->exec("ALTER TABLE quiz_sessions ADD COLUMN academic_level_id INT NULL AFTER department_id");
        echo "<div class='success'>✅ Added department_id and academic_level_id columns to quiz_sessions table</div>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "<div class='info'>ℹ️ Columns already exist in quiz_sessions table</div>";
        } else {
            echo "<div class='error'>❌ Failed to add columns: " . $e->getMessage() . "</div>";
        }
    }
    
    // Migrate existing quiz sessions data
    try {
        $pdo->exec("
            UPDATE quiz_sessions qs 
            JOIN students s ON qs.student_id = s.id 
            SET qs.department_id = s.department_id, 
                qs.academic_level_id = s.academic_level_id
            WHERE qs.department_id IS NULL
        ");
        echo "<div class='success'>✅ Migrated existing quiz sessions to use department + academic level</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Failed to migrate quiz sessions: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>📈 Step 5: Update Student Progress Table Structure</h2>";
    
    // Drop and recreate student_progress table with new structure
    try {
        $pdo->exec("DROP TABLE IF EXISTS student_progress");
        $pdo->exec("
            CREATE TABLE student_progress (
                id INT PRIMARY KEY AUTO_INCREMENT,
                student_id INT NOT NULL,
                department_id INT NOT NULL,
                academic_level_id INT NOT NULL,
                questions_attempted INT DEFAULT 0,
                questions_correct INT DEFAULT 0,
                average_score DECIMAL(5,2) DEFAULT 0.00,
                time_spent INT DEFAULT 0,
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                mastery_level ENUM('Beginner', 'Intermediate', 'Advanced', 'Expert') DEFAULT 'Beginner',
                FOREIGN KEY (student_id) REFERENCES students(id),
                FOREIGN KEY (department_id) REFERENCES departments(id),
                FOREIGN KEY (academic_level_id) REFERENCES academic_levels(id),
                UNIQUE KEY unique_student_dept_level (student_id, department_id, academic_level_id)
            )
        ");
        echo "<div class='success'>✅ Recreated student_progress table with department + academic level structure</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Failed to recreate student_progress table: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>🗑️ Step 6: Remove Subject Dependencies</h2>";
    
    // Remove foreign key constraints and subject_id columns
    try {
        // Drop foreign key constraints first
        $pdo->exec("ALTER TABLE questions DROP FOREIGN KEY questions_ibfk_1");
        echo "<div class='success'>✅ Removed foreign key constraint from questions table</div>";
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ Foreign key constraint may not exist: " . $e->getMessage() . "</div>";
    }
    
    try {
        $pdo->exec("ALTER TABLE quiz_sessions DROP FOREIGN KEY quiz_sessions_ibfk_2");
        echo "<div class='success'>✅ Removed foreign key constraint from quiz_sessions table</div>";
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ Foreign key constraint may not exist: " . $e->getMessage() . "</div>";
    }
    
    // Drop subject_id columns
    try {
        $pdo->exec("ALTER TABLE questions DROP COLUMN subject_id");
        echo "<div class='success'>✅ Removed subject_id column from questions table</div>";
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ subject_id column may not exist: " . $e->getMessage() . "</div>";
    }
    
    try {
        $pdo->exec("ALTER TABLE quiz_sessions DROP COLUMN subject_id");
        echo "<div class='success'>✅ Removed subject_id column from quiz_sessions table</div>";
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ subject_id column may not exist: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>🎯 Step 7: Add New Foreign Key Constraints</h2>";
    
    // Add foreign key constraints for new structure
    try {
        $pdo->exec("ALTER TABLE questions ADD FOREIGN KEY (department_id) REFERENCES departments(id)");
        $pdo->exec("ALTER TABLE questions ADD FOREIGN KEY (academic_level_id) REFERENCES academic_levels(id)");
        echo "<div class='success'>✅ Added foreign key constraints to questions table</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Failed to add foreign keys to questions: " . $e->getMessage() . "</div>";
    }
    
    try {
        $pdo->exec("ALTER TABLE quiz_sessions ADD FOREIGN KEY (department_id) REFERENCES departments(id)");
        $pdo->exec("ALTER TABLE quiz_sessions ADD FOREIGN KEY (academic_level_id) REFERENCES academic_levels(id)");
        echo "<div class='success'>✅ Added foreign key constraints to quiz_sessions table</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Failed to add foreign keys to quiz_sessions: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>🗂️ Step 8: Remove Subjects Table</h2>";
    
    try {
        $pdo->exec("DROP TABLE subjects");
        echo "<div class='success'>✅ Subjects table removed successfully</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Failed to remove subjects table: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>✅ Migration Complete!</h2>";
    echo "<div class='success'>";
    echo "<h3>🎉 Database Migration Successful!</h3>";
    echo "<p><strong>The LMS now uses a simplified architecture:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Questions are linked directly to Department + Academic Level</li>";
    echo "<li>✅ Quiz sessions track Department + Academic Level</li>";
    echo "<li>✅ Student progress tracks Department + Academic Level</li>";
    echo "<li>✅ Subjects table removed</li>";
    echo "<li>✅ All data preserved in backup tables</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='backup-info'>";
    echo "<h3>📦 Backup Information:</h3>";
    echo "<p>All original data has been backed up in these tables:</p>";
    echo "<ul>";
    echo "<li><code>subjects_backup</code> - Original subjects data</li>";
    echo "<li><code>questions_backup</code> - Original questions data</li>";
    echo "<li><code>quiz_sessions_backup</code> - Original quiz sessions data</li>";
    echo "<li><code>student_progress_backup</code> - Original progress data</li>";
    echo "</ul>";
    echo "<p><strong>These backup tables can be removed after confirming the migration works correctly.</strong></p>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ Migration Failed</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p>The migration has been stopped. Please check the error and try again.</p>";
    echo "</div>";
}

echo "<br><br>";
echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='../admin/dashboard.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>📊 Go to Dashboard</a>";
echo "<a href='../test-system.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🧪 Test System</a>";
echo "</div>";

echo "</body></html>";
?>
