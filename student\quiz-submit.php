<?php
/**
 * Quiz Submission Handler for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Check if student is logged in
startSecureSession();
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'student') {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit();
}

$quizSessionId = $_POST['quiz_session_id'] ?? null;
$currentQuestion = $_POST['current_question'] ?? null;
$selectedAnswer = $_POST['answer'] ?? null;
$action = $_POST['action'] ?? 'save';

if (!$quizSessionId || !$currentQuestion) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
    exit();
}

try {
    // Verify quiz session belongs to current student
    $quizSession = fetchOne("
        SELECT * FROM quiz_sessions 
        WHERE id = :id AND student_id = :student_id AND status = 'in_progress'
    ", ['id' => $quizSessionId, 'student_id' => $_SESSION['user_id']]);

    if (!$quizSession) {
        http_response_code(404);
        echo json_encode(['success' => false, 'error' => 'Quiz session not found or already completed']);
        exit();
    }

    // Get the question for this quiz session
    $questionData = fetchOne("
        SELECT qq.*, q.correct_answer, q.question_text
        FROM quiz_questions qq
        JOIN questions q ON qq.question_id = q.id
        WHERE qq.quiz_session_id = :quiz_id AND qq.question_number = :question_num
    ", ['quiz_id' => $quizSessionId, 'question_num' => $currentQuestion]);

    if (!$questionData) {
        http_response_code(404);
        echo json_encode(['success' => false, 'error' => 'Question not found']);
        exit();
    }

    // Save or update the answer if provided
    if ($selectedAnswer) {
        // Check if answer already exists
        $existingAnswer = fetchOne("
            SELECT * FROM quiz_answers 
            WHERE quiz_session_id = :quiz_id AND question_number = :question_num
        ", ['quiz_id' => $quizSessionId, 'question_num' => $currentQuestion]);

        $isCorrect = ($selectedAnswer === $questionData['correct_answer']) ? 1 : 0;

        if ($existingAnswer) {
            // Update existing answer
            execute("
                UPDATE quiz_answers 
                SET selected_answer = :answer, is_correct = :is_correct, answered_at = NOW()
                WHERE quiz_session_id = :quiz_id AND question_number = :question_num
            ", [
                'answer' => $selectedAnswer,
                'is_correct' => $isCorrect,
                'quiz_id' => $quizSessionId,
                'question_num' => $currentQuestion
            ]);
        } else {
            // Insert new answer
            insertRecord('quiz_answers', [
                'quiz_session_id' => $quizSessionId,
                'question_id' => $questionData['question_id'],
                'question_number' => $currentQuestion,
                'selected_answer' => $selectedAnswer,
                'is_correct' => $isCorrect,
                'answered_at' => date('Y-m-d H:i:s')
            ]);
        }
    }

    // Handle different actions
    if ($action === 'finish') {
        // Complete the quiz
        completeQuiz($quizSessionId);
        
        if (isset($_POST['navigate_to'])) {
            echo json_encode(['success' => true, 'redirect' => 'quiz-result.php?id=' . $quizSessionId]);
        } else {
            header('Location: quiz-result.php?id=' . $quizSessionId);
        }
        exit();
    } elseif ($action === 'next') {
        // Move to next question
        $nextQuestion = $currentQuestion + 1;
        
        // Get total questions
        $totalQuestions = fetchOne("
            SELECT COUNT(*) as total FROM quiz_questions 
            WHERE quiz_session_id = :quiz_id
        ", ['quiz_id' => $quizSessionId])['total'];

        if ($nextQuestion > $totalQuestions) {
            // This was the last question, complete the quiz
            completeQuiz($quizSessionId);
            header('Location: quiz-result.php?id=' . $quizSessionId);
        } else {
            // Go to next question
            header('Location: quiz.php?action=take&quiz_id=' . $quizSessionId . '&question=' . $nextQuestion);
        }
        exit();
    } elseif ($action === 'save') {
        // Just save the answer (AJAX call)
        echo json_encode(['success' => true, 'message' => 'Answer saved']);
        exit();
    }

} catch (Exception $e) {
    error_log("Quiz submission error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
    exit();
}

/**
 * Complete the quiz and calculate final score
 */
function completeQuiz($quizSessionId) {
    try {
        // Calculate score
        $scoreData = fetchOne("
            SELECT 
                COUNT(*) as total_questions,
                SUM(is_correct) as correct_answers
            FROM quiz_answers 
            WHERE quiz_session_id = :quiz_id
        ", ['quiz_id' => $quizSessionId]);

        $totalQuestions = $scoreData['total_questions'];
        $correctAnswers = $scoreData['correct_answers'] ?? 0;
        $score = $totalQuestions > 0 ? ($correctAnswers / $totalQuestions) * 100 : 0;

        // Update quiz session
        execute("
            UPDATE quiz_sessions 
            SET 
                status = 'completed',
                score = :score,
                correct_answers = :correct_answers,
                total_questions = :total_questions,
                completed_at = NOW(),
                time_taken = TIMESTAMPDIFF(SECOND, started_at, NOW())
            WHERE id = :quiz_id
        ", [
            'score' => round($score, 2),
            'correct_answers' => $correctAnswers,
            'total_questions' => $totalQuestions,
            'quiz_id' => $quizSessionId
        ]);

        // Log completion
        insertRecord('activity_logs', [
            'user_id' => $_SESSION['user_id'],
            'user_type' => 'student',
            'action' => 'quiz_completed',
            'details' => "Completed quiz with score: {$score}% ({$correctAnswers}/{$totalQuestions})",
            'ip_address' => $_SERVER['REMOTE_ADDR']
        ]);

        return true;
    } catch (Exception $e) {
        error_log("Error completing quiz: " . $e->getMessage());
        return false;
    }
}
?>
