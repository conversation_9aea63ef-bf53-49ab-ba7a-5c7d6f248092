<?php
/**
 * Database Setup Script for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 * 
 * This script creates the database and sets up all tables
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database_name = 'lms_ogbonnaya_onu';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Database Setup - AI-Powered LMS</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { color: #16a34a; background: #f0fdf4; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc2626; background: #fef2f2; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0369a1; background: #f0f9ff; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #6366f1; background: #f8fafc; }
        h1 { color: #1f2937; }
        h2 { color: #374151; }
        pre { background: #f3f4f6; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>AI-Powered LMS Database Setup</h1>
    <p>Setting up database for Ogbonnaya Onu Polytechnic, Aba</p>";

try {
    // Step 1: Connect to MySQL server (without database)
    echo "<div class='step'><h2>Step 1: Connecting to MySQL Server</h2>";
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<div class='success'>✓ Connected to MySQL server successfully</div>";
    
    // Step 2: Create database if it doesn't exist
    echo "<h2>Step 2: Creating Database</h2>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<div class='success'>✓ Database '$database_name' created successfully</div>";
    
    // Step 3: Connect to the specific database
    echo "<h2>Step 3: Connecting to Database</h2>";
    $pdo = new PDO("mysql:host=$host;dbname=$database_name", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<div class='success'>✓ Connected to database '$database_name' successfully</div></div>";
    
    // Step 4: Read and execute schema
    echo "<div class='step'><h2>Step 4: Creating Database Tables</h2>";
    $schemaFile = 'database/schema.sql';
    
    if (!file_exists($schemaFile)) {
        throw new Exception("Schema file not found: $schemaFile");
    }
    
    $schema = file_get_contents($schemaFile);
    
    // Split the schema into individual statements
    $statements = array_filter(array_map('trim', explode(';', $schema)));
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
                // Extract table name for display
                if (preg_match('/CREATE TABLE\s+`?(\w+)`?/i', $statement, $matches)) {
                    echo "<div class='success'>✓ Created table: {$matches[1]}</div>";
                }
            } catch (PDOException $e) {
                echo "<div class='error'>✗ Error executing statement: " . $e->getMessage() . "</div>";
                echo "<pre>" . htmlspecialchars($statement) . "</pre>";
            }
        }
    }
    echo "</div>";
    
    // Step 5: Insert initial data
    echo "<div class='step'><h2>Step 5: Inserting Initial Data</h2>";
    $dataFile = 'database/initial_data.sql';
    
    if (file_exists($dataFile)) {
        $initialData = file_get_contents($dataFile);
        $dataStatements = array_filter(array_map('trim', explode(';', $initialData)));
        
        foreach ($dataStatements as $statement) {
            if (!empty($statement)) {
                try {
                    $pdo->exec($statement);
                    // Extract table name for display
                    if (preg_match('/INSERT INTO\s+`?(\w+)`?/i', $statement, $matches)) {
                        echo "<div class='success'>✓ Inserted data into: {$matches[1]}</div>";
                    }
                } catch (PDOException $e) {
                    echo "<div class='error'>✗ Error inserting data: " . $e->getMessage() . "</div>";
                }
            }
        }
    } else {
        echo "<div class='info'>ℹ Initial data file not found, skipping data insertion</div>";
    }
    echo "</div>";
    
    // Step 6: Verify setup
    echo "<div class='step'><h2>Step 6: Verifying Database Setup</h2>";
    
    // Check if tables exist
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "<div class='success'>✓ Found " . count($tables) . " tables:</div>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";
    
    // Check if admin setup is needed
    $adminCount = $pdo->query("SELECT COUNT(*) FROM admins")->fetchColumn();
    if ($adminCount == 0) {
        echo "<div class='info'>ℹ No admin accounts found. You'll need to run the admin setup.</div>";
    } else {
        echo "<div class='success'>✓ Found $adminCount admin account(s)</div>";
    }
    
    echo "</div>";
    
    echo "<div class='step' style='border-left-color: #16a34a; background: #f0fdf4;'>
            <h2 style='color: #16a34a;'>✓ Database Setup Complete!</h2>
            <p>Your AI-Powered LMS database has been set up successfully.</p>
            <h3>Next Steps:</h3>
            <ol>
                <li><a href='setup-admin.php'>Set up your admin account</a></li>
                <li><a href='index.php'>Visit the main page</a></li>
                <li><a href='admin/login.php'>Access admin panel</a></li>
                <li><a href='student/login.php'>Access student portal</a></li>
            </ol>
          </div>";
    
} catch (Exception $e) {
    echo "<div class='error'>
            <h2>Setup Failed</h2>
            <p><strong>Error:</strong> " . $e->getMessage() . "</p>
            <h3>Troubleshooting:</h3>
            <ul>
                <li>Make sure MySQL/MariaDB is running</li>
                <li>Check your database credentials in this file</li>
                <li>Ensure the web server has permission to create databases</li>
                <li>Make sure the database schema files exist in the 'database' folder</li>
            </ul>
          </div>";
}

echo "</body></html>";
?>
